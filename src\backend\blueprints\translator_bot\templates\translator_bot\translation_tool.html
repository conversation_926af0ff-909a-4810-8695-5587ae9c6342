<!-- templates/translator_bot/translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='translator_tool.css') }}">
<!-- css dependencies for pptx.js -->
<!-- <link rel="stylesheet" href="https://github.com/meshesha/PPTXjs/css/pptxjs.css">
<link rel="stylesheet" href="https://github.com/meshesha/PPTXjs/css/nv.d3.min.css"> for charts graphs -->
<style>
    /* Custom multi-select styling to match source language selector */
    .target-language-selector {
        position: relative;
        z-index: 10000;
    }

    .source-language-selector {
        position: relative;
        z-index: 10000;
    }

    .target-language-container,
    .source-language-container {
        background-color: #fff;
        border: 2px solid #dee2e6;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        min-height: 56px;
        cursor: pointer;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        color: #212529;
        font-weight: 400;
        font-size: 1rem;
        line-height: 1.5;
        display: flex;
        align-items: center;
    }

    .target-language-container:hover,
    .source-language-container:hover {
        border-color: #86b7fe;
    }

    .target-language-container.open,
    .source-language-container.open {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .target-language-placeholder,
    .source-language-display {
        color: #6c757d;
        font-weight: 400;
        font-size: 1rem;
    }

    .selected-languages {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        min-height: 1.5rem;
    }

    .language-tag {
        background: #24639f;
        color: white;
        padding: 0.4rem 0.75rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .language-tag .remove-btn {
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        padding: 0;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .language-tag .remove-btn:hover {
        color: white;
        background: rgba(255, 255, 255, 0.2);
    }

    .target-language-dropdown,
    .source-language-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 2px solid #24639f;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 99999;
        margin-top: 4px;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    }

    .target-language-dropdown.open,
    .source-language-dropdown.open {
        display: block;
    }

    .language-option,
    .source-language-option {
        padding: 0.75rem 1rem;
        cursor: pointer;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
    }

    .language-option:hover,
    .source-language-option:hover {
        background-color: #f8f9fa;
    }

    .language-option.selected,
    .source-language-option.selected {
        background-color: #e3f2fd;
        color: #24639f;
        font-weight: 600;
    }

    .language-option:last-child,
    .source-language-option:last-child {
        border-bottom: none;
    }

    /* Ensure preview header text and icon are white */
    #previewSection .excel-options-header h5,
    #previewSection .excel-options-header h5 i {
        color: white !important;
    }


    .ppt-preview-container {
        margin-top: 20px;
    }

    .ppt-controls {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        margin-bottom: 15px;
    }

    .ppt-slide-container {
        min-height: 500px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        padding: 20px;
    }

    .ppt-slide {
        background: white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-radius: 4px;
    }

    .btn-group .btn.disabled {
        pointer-events: none;
        background-color: #e9ecef;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .ppt-controls .row>div {
            margin-bottom: 10px;
        }

        .ppt-controls .text-end {
            text-align: left !important;
        }

        .ppt-slide {
            max-width: 100%;
            height: auto;
            aspect-ratio: 16/9;
        }
    }
</style>


</style>
{% endblock %}

{% block content %}
<div class="translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="translation-card">
                    <div class="card-header-custom position-relative text-center"
                        style="display: flex; flex-direction: column; align-items: center; padding-top: 0;">
                        <span class="header-icon-top"
                            style="font-size: 5rem; margin-bottom: 0.1rem; margin-top: 0; display: block;">
                            <i class="fas fa-language"></i>
                        </span>
                        <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 700; color: whitesmoke;">MyEPRO AI
                            Translate</h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Translate documents with the help of Artificial Intelligence
                        </p>
                    </div>

                    <div class="card-body p-4">
                        <!-- File Upload Section -->
                        <div class="mb-4 mt-4">
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & drop your file here</h6>
                                <p class="text-muted mb-3">or click to browse</p>
                                <p class="small text-muted">
                                    Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx), PDF (.pdf)
                                    <br>Maximum file size: 2GB
                                </p>
                                <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx,.pdf"
                                    style="display: none;">
                            </div>

                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file me-3 text-dark"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <div class="small text-muted" id="fileDetails"></div>
                                    </div>
                                    <button class="remove-file-btn" id="removeFile" type="button">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Loading Spinner -->
                            <div id="loadingSpinner" class="loading-spinner">
                                <div class="spinner"></div>
                                <div class="loading-text" id="loadingText">Uploading and processing file...</div>
                            </div>
                        </div>

                        <!-- File Context Input & Language Selection -->
                        <div id="languageSettings" class="excel-options mt-4" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-globe me-2"></i>
                                    Language Settings
                                </h5>
                            </div>
                            
                            <div class="language-grid">
                                <div>
                                    <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                    <div class="source-language-selector">
                                        <div class="source-language-container" id="sourceLanguageContainer">
                                            <div class="selected-language" id="selectedSourceLanguage">
                                                <div class="source-language-display">Auto-detect</div>
                                            </div>
                                        </div>
                                        <div class="source-language-dropdown" id="sourceLanguageDropdown">
                                            <div class="source-language-option" data-value="auto">Auto-detect</div>
                                            <div class="source-language-option" data-value="ar">Arabic</div>
                                            <div class="source-language-option" data-value="zh">Chinese</div>
                                            <div class="source-language-option" data-value="da">Danish</div>
                                            <div class="source-language-option" data-value="nl">Dutch</div>
                                            <div class="source-language-option" data-value="en">English</div>
                                            <div class="source-language-option" data-value="fi">Finnish</div>
                                            <div class="source-language-option" data-value="fr">French</div>
                                            <div class="source-language-option" data-value="de">German</div>
                                            <div class="source-language-option" data-value="hi">Hindi</div>
                                            <div class="source-language-option" data-value="it">Italian</div>
                                            <div class="source-language-option" data-value="ja">Japanese</div>
                                            <div class="source-language-option" data-value="ko">Korean</div>
                                            <div class="source-language-option" data-value="no">Norwegian</div>
                                            <div class="source-language-option" data-value="pl">Polish</div>
                                            <div class="source-language-option" data-value="pt">Portuguese</div>
                                            <div class="source-language-option" data-value="ru">Russian</div>
                                            <div class="source-language-option" data-value="es">Spanish</div>
                                            <div class="source-language-option" data-value="sv">Swedish</div>
                                            <div class="source-language-option" data-value="th">Thai</div>
                                            <div class="source-language-option" data-value="tu">Turkish</div>
                                            <div class="source-language-option" data-value="vi">Vietnamese</div>
                                        </div>
                                        <!-- Hidden select for form compatibility -->
                                        <select class="form-select form-select-custom" id="sourceLanguage"
                                            style="display: none;">
                                            <option value="auto" selected>Auto-detect</option>
                                            <option value="ar">Arabic</option>
                                            <option value="zh">Chinese</option>
                                            <option value="da">Danish</option>
                                            <option value="nl">Dutch</option>
                                            <option value="en">English</option>
                                            <option value="fi">Finnish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                            <option value="hi">Hindi</option>
                                            <option value="it">Italian</option>
                                            <option value="ja">Japanese</option>
                                            <option value="ko">Korean</option>
                                            <option value="no">Norwegian</option>
                                            <option value="pl">Polish</option>
                                            <option value="pt">Portuguese</option>
                                            <option value="ru">Russian</option>
                                            <option value="es">Spanish</option>
                                            <option value="sv">Swedish</option>
                                            <option value="th">Thai</option>
                                            <option value="tu">Turkish</option>
                                            <option value="vi">Vietnamese</option>
                                        </select>
                                    </div>
                                </div>
                                <div>
                                    <label for="targetLanguage" class="form-label fw-semibold">Target
                                        Language(s)</label>
                                    <div class="target-language-selector">
                                        <div class="target-language-container" id="targetLanguageContainer">
                                            <div class="selected-languages" id="selectedLanguages">
                                                <div class="target-language-placeholder" id="targetLanguagePlaceholder">
                                                    Select target language(s)
                                                </div>
                                            </div>
                                        </div>
                                        <div class="target-language-dropdown" id="targetLanguageDropdown">
                                            <div class="language-option" data-value="ar">Arabic</div>
                                            <div class="language-option" data-value="zh">Chinese</div>
                                            <div class="language-option" data-value="da">Danish</div>
                                            <div class="language-option" data-value="nl">Dutch</div>
                                            <div class="language-option" data-value="en">English</div>
                                            <div class="language-option" data-value="fi">Finnish</div>
                                            <div class="language-option" data-value="fr">French</div>
                                            <div class="language-option" data-value="de">German</div>
                                            <div class="language-option" data-value="hi">Hindi</div>
                                            <div class="language-option" data-value="it">Italian</div>
                                            <div class="language-option" data-value="ja">Japanese</div>
                                            <div class="language-option" data-value="ko">Korean</div>
                                            <div class="language-option" data-value="no">Norwegian</div>
                                            <div class="language-option" data-value="pl">Polish</div>
                                            <div class="language-option" data-value="pt">Portuguese</div>
                                            <div class="language-option" data-value="ru">Russian</div>
                                            <div class="language-option" data-value="es">Spanish</div>
                                            <div class="language-option" data-value="sv">Swedish</div>
                                            <div class="language-option" data-value="th">Thai</div>
                                            <div class="language-option" data-value="tu">Turkish</div>
                                            <div class="language-option" data-value="vi">Vietnamese</div>
                                        </div>
                                        <!-- Hidden select for form compatibility -->
                                        <select class="form-select form-select-custom" id="targetLanguage"
                                            multiple="multiple" style="display: none;">
                                            <option value="ar">Arabic</option>
                                            <option value="zh">Chinese</option>
                                            <option value="da">Danish</option>
                                            <option value="nl">Dutch</option>
                                            <option value="en">English</option>
                                            <option value="fi">Finnish</option>
                                            <option value="fr">French</option>
                                            <option value="de">German</option>
                                            <option value="hi">Hindi</option>
                                            <option value="it">Italian</option>
                                            <option value="ja">Japanese</option>
                                            <option value="ko">Korean</option>
                                            <option value="no">Norwegian</option>
                                            <option value="pl">Polish</option>
                                            <option value="pt">Portuguese</option>
                                            <option value="ru">Russian</option>
                                            <option value="es">Spanish</option>
                                            <option value="sv">Swedish</option>
                                            <option value="th">Thai</option>
                                            <option value="tu">Turkish</option>
                                            <option value="vi">Vietnamese</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Context Input -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label for="fileContext" class="form-label fw-semibold mb-0">File Context (optional)</label>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="regeneratePromptBtn" title="Generate context prompt based on uploaded file">
                                        <i class="fas fa-magic me-1"></i>Generate Prompt
                                    </button>
                                </div>
                                <textarea class="form-control" id="fileContext" rows="3" placeholder="Add any relevant context or instructions for the model to improve the translation accuracy. Click 'Generate Prompt' to auto-populate based on your uploaded file."></textarea>
                            </div>
                        </div>


                        <!-- File Options (Excel columns, Word paragraphs, PPTX slides) -->
                        <div id="fileOptions" class="excel-options" style="display: none;">
                            <div class="excel-options-header">
                                <h5 id="fileOptionsHeader"></h5>
                                <button type="button" class="select-all-btn" id="toggleAllColumns"
                                    style="display:none;"><i class="fas fa-check-square me-1"></i>Deselect All</button>
                            </div>
                            <p class="text-muted mb-3" id="fileOptionsDescription"></p>
                            <div class="row" id="optionCheckboxes">
                                <!-- Checkboxes will be populated dynamically -->
                            </div>
                        </div>


                        <!-- Preview Section -->
                        <div id="previewSection" class="excel-options mt-4" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-eye me-2"></i>
                                    Translation Preview
                                </h5>
                                <div class="header-decoration"></div>
                            </div>
                            <p class="text-muted mb-3" id="previewDescription">Preview of the translation in <span
                                    id="previewTargetLanguage"></span> of random cells taken from the <span
                                    id="previewColumnCount">1</span> selected column(s):</p>

                            <div class="preview-table-container">
                                <table class="table table-striped table-hover" id="previewTable">
                                    <thead class="table-dark" id="previewTableHead">
                                        <tr>
                                            <th style="width: 8%;">Row</th>
                                            <th style="width: 15%;">Column</th>
                                            <th style="width: 38%;">Original</th>
                                            <th style="width: 39%;">Translated</th>
                                        </tr>
                                    </thead>
                                    <tbody id="previewTableBody">
                                        <!-- Preview data will be populated here -->
                                    </tbody>
                                </table>
                            </div>

                            <div class="preview-actions text-center mt-4">
                                <button class="btn btn-outline-secondary me-3" id="newPreviewBtn">
                                    <i class="fas fa-redo me-2"></i>
                                    New Preview
                                </button>
                                <button class="btn btn-success btn-lg" id="continueTranslationBtn">
                                    <i class="fas fa-check me-2"></i>
                                    Continue with Full Translation
                                </button>
                            </div>
                        </div>

                        <!-- Translation Button -->
                        <div class="text-center mt-4" id="translateButtonSection">
                            <button class="btn btn-outline-primary me-3" id="previewBtn" style="display: none;">
                                <i class="fas fa-eye me-2"></i>
                                Preview Translation
                            </button>
                            <button class="translate-link-custom btn-lg" id="translateBtn" disabled>
                                <i class="fas fa-magic me-2"></i>
                                Start Translation
                            </button>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<!-- JSZip (required for docx-preview) -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<!-- docx-preview JS - Latest version with CDN fallback -->
<script src="https://cdn.jsdelivr.net/npm/docx-preview@0.3.6/dist/docx-preview.min.js"
    onload="console.log('docx-preview loaded from CDN:', typeof window.docx)"
    onerror="console.warn('CDN failed, loading local fallback'); loadDocxPreviewFallback();">
    </script>
<script>
    // Fallback loader for docx-preview
    function loadDocxPreviewFallback() {
        const script = document.createElement('script');
        script.src = "{{ url_for('static', filename='docx-preview.min.js') }}";
        script.onload = function () {
            console.log('docx-preview loaded from local fallback:', typeof window.docx);
        };
        script.onerror = function () {
            console.error('Failed to load docx-preview from both CDN and local fallback');
        };
        document.head.appendChild(script);
    }
</script>


<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize custom multi-select for target languages
        // Initialize custom selectors
        initializeTargetLanguageSelector();
        initializeSourceLanguageSelector();

        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileDetails = document.getElementById('fileDetails');
        const removeFile = document.getElementById('removeFile');
        const fileOptions = document.getElementById('fileOptions');
        const optionCheckboxes = document.getElementById('optionCheckboxes');
        const toggleAllColumns = document.getElementById('toggleAllColumns');
        const translateBtn = document.getElementById('translateBtn');
        const targetLanguage = document.getElementById('targetLanguage');

        const loadingSpinner = document.getElementById('loadingSpinner');
        const loadingText = document.getElementById('loadingText');
        const languageSettings = document.getElementById('languageSettings');
        const previewSection = document.getElementById('previewSection');
        const previewBtn = document.getElementById('previewBtn');
        const newPreviewBtn = document.getElementById('newPreviewBtn');
        const continueTranslationBtn = document.getElementById('continueTranslationBtn');

        let selectedFile = null;
        let fileType = null;
        let currentSessionId = null; // Track current translation session
        let previewData = null; // Store current preview data
        let translatedWordPreviewLoaded = {}; // Track which translated previews have been loaded
        let selectedTargetLanguages = []; // Store selected target languages

        // Ensure the translate button is always disabled on page load
        translateBtn.hidden = true;

        // Custom target language selector functionality
        function initializeTargetLanguageSelector() {
            const container = document.getElementById('targetLanguageContainer');
            const dropdown = document.getElementById('targetLanguageDropdown');
            const selectedLanguagesDiv = document.getElementById('selectedLanguages');
            const placeholder = document.getElementById('targetLanguagePlaceholder');
            const hiddenSelect = document.getElementById('targetLanguage');
            const options = dropdown.querySelectorAll('.language-option');

            // Toggle dropdown
            container.addEventListener('click', function (e) {
                e.stopPropagation();
                const isOpen = dropdown.classList.contains('open');

                if (isOpen) {
                    dropdown.classList.remove('open');
                    container.classList.remove('open');
                    dropdown.style.display = 'none';
                    // Remove scroll listener
                    window.removeEventListener('scroll', updateDropdownPosition);
                    window.removeEventListener('resize', updateDropdownPosition);
                    // Move back to original position
                    if (dropdown.parentElement === document.body) {
                        document.querySelector('.target-language-selector').appendChild(dropdown);
                        dropdown.style.position = 'absolute';
                        dropdown.style.left = '0';
                        dropdown.style.top = '100%';
                        dropdown.style.width = 'auto';
                    }
                } else {
                    // Move dropdown to body to avoid overflow issues
                    document.body.appendChild(dropdown);

                    // Function to update dropdown position
                    function updateDropdownPosition() {
                        const rect = container.getBoundingClientRect();
                        dropdown.style.left = rect.left + 'px';
                        dropdown.style.top = (rect.bottom + 4) + 'px';
                        dropdown.style.width = rect.width + 'px';
                    }

                    // Set initial position
                    dropdown.style.position = 'fixed';
                    dropdown.style.zIndex = '99999';
                    updateDropdownPosition();

                    // Add scroll and resize listeners to keep dropdown positioned
                    window.addEventListener('scroll', updateDropdownPosition);
                    window.addEventListener('resize', updateDropdownPosition);

                    // Store the update function for later cleanup
                    window.updateDropdownPosition = updateDropdownPosition;

                    dropdown.classList.add('open');
                    container.classList.add('open');
                    dropdown.style.display = 'block';
                }
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function () {
                // Close target language dropdown
                dropdown.classList.remove('open');
                container.classList.remove('open');
                dropdown.style.display = 'none';
                // Remove scroll and resize listeners
                if (window.updateDropdownPosition) {
                    window.removeEventListener('scroll', window.updateDropdownPosition);
                    window.removeEventListener('resize', window.updateDropdownPosition);
                    window.updateDropdownPosition = null;
                }
                // Move dropdown back to its original position
                if (dropdown.parentElement === document.body) {
                    document.querySelector('.target-language-selector').appendChild(dropdown);
                    dropdown.style.position = 'absolute';
                    dropdown.style.left = '0';
                    dropdown.style.top = '100%';
                    dropdown.style.width = 'auto';
                }

                // Close source language dropdown if it exists
                const sourceDropdown = document.getElementById('sourceLanguageDropdown');
                const sourceContainer = document.getElementById('sourceLanguageContainer');
                if (sourceDropdown && sourceContainer) {
                    sourceDropdown.classList.remove('open');
                    sourceContainer.classList.remove('open');
                    sourceDropdown.style.display = 'none';
                    // Remove scroll and resize listeners
                    if (window.updateSourceDropdownPosition) {
                        window.removeEventListener('scroll', window.updateSourceDropdownPosition);
                        window.removeEventListener('resize', window.updateSourceDropdownPosition);
                        window.updateSourceDropdownPosition = null;
                    }
                    // Move dropdown back to its original position
                    if (sourceDropdown.parentElement === document.body) {
                        document.querySelector('.source-language-selector').appendChild(sourceDropdown);
                        sourceDropdown.style.position = 'absolute';
                        sourceDropdown.style.left = '0';
                        sourceDropdown.style.top = '100%';
                        sourceDropdown.style.width = 'auto';
                    }
                }
            });

            // Handle language option selection
            options.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.stopPropagation();
                    const value = this.dataset.value;
                    const text = this.textContent;

                    if (this.classList.contains('selected')) {
                        // Remove language
                        removeTargetLanguage(value);
                    } else {
                        // Add language
                        addTargetLanguage(value, text);
                    }
                });
            });

            function addTargetLanguage(value, text) {
                if (!selectedTargetLanguages.includes(value)) {
                    selectedTargetLanguages.push(value);

                    // Update UI
                    updateSelectedLanguagesDisplay();
                    updateHiddenSelect();
                    updateDropdownOptions();

                    // Trigger change event
                    triggerChangeEvent();
                }
            }

            function removeTargetLanguage(value) {
                const index = selectedTargetLanguages.indexOf(value);
                if (index > -1) {
                    selectedTargetLanguages.splice(index, 1);

                    // Update UI
                    updateSelectedLanguagesDisplay();
                    updateHiddenSelect();
                    updateDropdownOptions();

                    // Trigger change event
                    triggerChangeEvent();
                }
            }

            function updateSelectedLanguagesDisplay() {
                selectedLanguagesDiv.innerHTML = '';

                if (selectedTargetLanguages.length === 0) {
                    selectedLanguagesDiv.appendChild(placeholder);
                } else {
                    selectedTargetLanguages.forEach(value => {
                        const text = getLanguageText(value);
                        const tag = document.createElement('div');
                        tag.className = 'language-tag';
                        tag.innerHTML = `
                        <span>${text}</span>
                        <button type="button" class="remove-btn" onclick="removeTargetLanguageByValue('${value}')">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                        selectedLanguagesDiv.appendChild(tag);
                    });
                }
            }

            function updateHiddenSelect() {
                // Clear all selections
                Array.from(hiddenSelect.options).forEach(option => {
                    option.selected = false;
                });

                // Set selected options
                selectedTargetLanguages.forEach(value => {
                    const option = hiddenSelect.querySelector(`option[value="${value}"]`);
                    if (option) {
                        option.selected = true;
                    }
                });
            }

            function updateDropdownOptions() {
                options.forEach(option => {
                    const value = option.dataset.value;
                    if (selectedTargetLanguages.includes(value)) {
                        option.classList.add('selected');
                    } else {
                        option.classList.remove('selected');
                    }
                });
            }

            function getLanguageText(value) {
                const option = document.querySelector(`.language-option[data-value="${value}"]`);
                return option ? option.textContent : value;
            }

            function triggerChangeEvent() {
                const event = new Event('change', { bubbles: true });
                hiddenSelect.dispatchEvent(event);
            }

            // Make removeTargetLanguageByValue globally available
            window.removeTargetLanguageByValue = removeTargetLanguage;
        }

        // Custom source language selector functionality
        function initializeSourceLanguageSelector() {
            const container = document.getElementById('sourceLanguageContainer');
            const dropdown = document.getElementById('sourceLanguageDropdown');
            const displayDiv = document.querySelector('.source-language-display');
            const hiddenSelect = document.getElementById('sourceLanguage');
            const options = dropdown.querySelectorAll('.source-language-option');
            let selectedSourceLanguage = 'auto';

            // Toggle dropdown
            container.addEventListener('click', function (e) {
                e.stopPropagation();
                const isOpen = dropdown.classList.contains('open');

                if (isOpen) {
                    dropdown.classList.remove('open');
                    container.classList.remove('open');
                    dropdown.style.display = 'none';
                    // Remove scroll listener
                    if (window.updateSourceDropdownPosition) {
                        window.removeEventListener('scroll', window.updateSourceDropdownPosition);
                        window.removeEventListener('resize', window.updateSourceDropdownPosition);
                        window.updateSourceDropdownPosition = null;
                    }
                    // Move back to original position
                    if (dropdown.parentElement === document.body) {
                        document.querySelector('.source-language-selector').appendChild(dropdown);
                        dropdown.style.position = 'absolute';
                        dropdown.style.left = '0';
                        dropdown.style.top = '100%';
                        dropdown.style.width = 'auto';
                    }
                } else {
                    // Move dropdown to body to avoid overflow issues
                    document.body.appendChild(dropdown);

                    // Function to update dropdown position
                    function updateSourceDropdownPosition() {
                        const rect = container.getBoundingClientRect();
                        dropdown.style.left = rect.left + 'px';
                        dropdown.style.top = (rect.bottom + 4) + 'px';
                        dropdown.style.width = rect.width + 'px';
                    }

                    // Set initial position
                    dropdown.style.position = 'fixed';
                    dropdown.style.zIndex = '99999';
                    updateSourceDropdownPosition();

                    // Add scroll and resize listeners to keep dropdown positioned
                    window.addEventListener('scroll', updateSourceDropdownPosition);
                    window.addEventListener('resize', updateSourceDropdownPosition);

                    // Store the update function for later cleanup
                    window.updateSourceDropdownPosition = updateSourceDropdownPosition;

                    dropdown.classList.add('open');
                    container.classList.add('open');
                    dropdown.style.display = 'block';
                }
            });

            // Handle language option selection
            options.forEach(option => {
                option.addEventListener('click', function (e) {
                    e.stopPropagation();
                    const value = this.dataset.value;
                    const text = this.textContent;

                    // Update selected language
                    selectedSourceLanguage = value;
                    displayDiv.textContent = text;

                    // Update hidden select
                    hiddenSelect.value = value;

                    // Trigger change event
                    const event = new Event('change', { bubbles: true });
                    hiddenSelect.dispatchEvent(event);

                    // Update visual state
                    updateDropdownOptions();

                    // Close dropdown
                    dropdown.classList.remove('open');
                    container.classList.remove('open');
                    dropdown.style.display = 'none';

                    // Clean up positioning
                    if (window.updateSourceDropdownPosition) {
                        window.removeEventListener('scroll', window.updateSourceDropdownPosition);
                        window.removeEventListener('resize', window.updateSourceDropdownPosition);
                        window.updateSourceDropdownPosition = null;
                    }
                    if (dropdown.parentElement === document.body) {
                        document.querySelector('.source-language-selector').appendChild(dropdown);
                        dropdown.style.position = 'absolute';
                        dropdown.style.left = '0';
                        dropdown.style.top = '100%';
                        dropdown.style.width = 'auto';
                    }
                });
            });

            function updateDropdownOptions() {
                options.forEach(option => {
                    const value = option.dataset.value;
                    if (value === selectedSourceLanguage) {
                        option.classList.add('selected');
                    } else {
                        option.classList.remove('selected');
                    }
                });
            }

            // Initialize with auto-detect selected
            updateDropdownOptions();
        }

        // Note: File cleanup now only happens on new file upload (server-side)
        // Removed automatic cleanup on page unload/visibility change to preserve user files

        // Helper function to show alerts
        function showAlert(message, type = 'info') {
            // Create a simple alert div
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
            document.body.appendChild(alertDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        // Upload area click handler
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop handlers
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        // File input change handler
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        // Remove file handler
        removeFile.addEventListener('click', () => {
            // Note: File cleanup now only happens on new file upload
            // Removed cleanup here to preserve user files unless they upload a new one
            console.log('removeFile clicked - UI reset only, no file cleanup');

            // Clear the context prompt text box when file is removed
            const fileContextTextarea = document.getElementById('fileContext');
            if (fileContextTextarea) {
                fileContextTextarea.value = '';
                console.log('Cleared context prompt when file removed');
            }

            selectedFile = null;
            currentSessionId = null;
            translationCompleted = false;
            fileInfo.style.display = 'none';
            uploadArea.style.display = 'block';
            fileOptions.style.display = 'none';
            loadingSpinner.style.display = 'none';
            fileInput.value = '';
            languageSettings.style.display = 'none';
            translatedWordPreviewLoaded = {};
            const existingDownloadLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
            existingDownloadLinks.forEach(link => link.remove());
            updateTranslateButton();
        });

        // Target language change handler (using standard event)
        targetLanguage.addEventListener('change', function () {
            updateTranslateButton();
            showTranslateButtonAfterChange();
        });

        // Update button state when columns are selected/deselected
        optionCheckboxes.addEventListener('change', function () {
            updateTranslateButton();
            showTranslateButtonAfterChange();
        });

    // Context change handler
    const fileContextTextarea = document.getElementById('fileContext');
    fileContextTextarea.addEventListener('input', showTranslateButtonAfterChange);

    // Regenerate prompt button handler
    const regeneratePromptBtn = document.getElementById('regeneratePromptBtn');
    regeneratePromptBtn.addEventListener('click', function() {
        generateContextPrompt(true); // Force regeneration
    });

        // Source language change handler
        const sourceLanguageSelect = document.getElementById('sourceLanguage');
        sourceLanguageSelect.addEventListener('change', showTranslateButtonAfterChange);

        // Translate button handler
        translateBtn.addEventListener('click', startTranslation);

        // Preview button handlers
        previewBtn.addEventListener('click', showTranslationPreview);
        newPreviewBtn.addEventListener('click', showTranslationPreview);
        continueTranslationBtn.addEventListener('click', startTranslation);

        // Toggle all columns button handler
        toggleAllColumns.addEventListener('click', function () {
            const checkboxContainer = document.getElementById('columnCheckboxes');
            if (!checkboxContainer) return;
            const checkboxes = checkboxContainer.querySelectorAll('input[type="checkbox"]');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(checkbox => {
                checkbox.checked = !allChecked;
            });

            // Update button text and icon safely
            let icon = this.querySelector('i');
            if (allChecked) {
                this.innerHTML = '<i class="fas fa-square me-1"></i>Select All';
            } else {
                this.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';
            }
            updateTranslateButton();
        });

        function handleFileSelect(file) {
            // Note: File cleanup now happens automatically on server-side during new upload
            // No need for manual cleanup here

            // Clear the context prompt text box when a new file is uploaded
            const fileContextTextarea = document.getElementById('fileContext');
            if (fileContextTextarea) {
                fileContextTextarea.value = '';
                console.log('Cleared context prompt for new file upload');
            }

            // Automatically detect file type by extension
            const allowedTypes = ['.xlsx', '.pptx', '.docx', '.pdf'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            fileType = fileExtension;
            console.log('File selected:', file.name, 'Detected file type:', fileType);
            if (!allowedTypes.includes(fileExtension)) {
                showAlert('Please select a valid file type: Excel (.xlsx), PowerPoint (.pptx), Word (.docx) or PDF (.pdf)', 'warning');
                return;
            }
            if (file.size > 2000 * 1024 * 1024) {
                showAlert('File size must be less than 2GB', 'warning');
                return;
            }
            selectedFile = file;
            currentSessionId = null; // Reset session ID for new file
            translationCompleted = false;
            languageSettings.style.display = 'none';
            uploadArea.style.display = 'none';
            loadingSpinner.style.display = 'block';
            loadingText.textContent = 'Uploading and processing file...';
            // All logic is now automatic based on file extension
            if (fileExtension === '.xlsx' || fileExtension === '.pptx' || fileExtension === '.docx' || fileExtension === '.pdf') {
                uploadFileToServer(file);
            } else {
                setTimeout(() => {
                    loadingSpinner.style.display = 'none';
                    fileName.textContent = file.name;
                    fileDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${fileExtension.toUpperCase().substring(1)} file`;
                    fileInfo.style.display = 'block';
                    showFileOptions(fileExtension);
                    updateTranslateButton();
                }, 800);
            }
        }

        function uploadFileToServer(file) {
            const formData = new FormData();
            formData.append('file', file);

            loadingText.textContent = 'Uploading file...';

            fetch('/translator/upload', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.type == '.xlsx') {
                            loadingText.textContent = 'Getting Excel table...';
                            getExcelColumns();
                        }
                        else if (data.type == '.pptx') {
                            loadingText.textContent = 'Getting PowerPoint slides...';
                            // TODO
                        }
                        else if (data.type == '.docx') {
                            loadingText.textContent = 'Getting Word paragraphs...';
                            // TODO: Add any specific handling for Word documents if needed
                        }
                        else if (data.type == '.pdf') {
                            loadingText.textContent = 'Getting PDF paragraphs...';
                        }
                        finishFileLoading(data.type);
                    } else {
                        loadingSpinner.style.display = 'none';
                        uploadArea.style.display = 'block';

                        // Clear the context prompt text box on upload error
                        const fileContextTextarea = document.getElementById('fileContext');
                        if (fileContextTextarea) {
                            fileContextTextarea.value = '';
                        }

                        // Reset file selection state
                        selectedFile = null;
                        currentSessionId = null;
                        fileInput.value = '';

                        showAlert('Error uploading file: ' + data.error, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Upload error:', error);
                    loadingSpinner.style.display = 'none';
                    uploadArea.style.display = 'block';

                    // Clear the context prompt text box on upload error
                    const fileContextTextarea = document.getElementById('fileContext');
                    if (fileContextTextarea) {
                        fileContextTextarea.value = '';
                    }

                    // Reset file selection state
                    selectedFile = null;
                    currentSessionId = null;
                    fileInput.value = '';

                    showAlert('Error uploading file', 'danger');
                });
        }

    function finishFileLoading(fileExtension) {
        // Hide loading spinner and show file info
        loadingSpinner.style.display = 'none';
        // Show language settings now that upload/processing is done
        languageSettings.style.display = 'block';
        // Update file info display
        fileName.textContent = selectedFile.name;
        fileInfo.style.display = 'block';

        // Generate and populate context prompt
        generateContextPrompt();

        updateTranslateButton();
    }

        function getExcelColumns() {
            loadingText.textContent = 'Analyzing Excel columns...';

            fetch('/translator/api/columns', {
                method: 'GET'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Use generic file options function for all file types
                        showFileOptions('.xlsx', data.columns);
                        finishFileLoading();
                    } else {
                        console.error('Error getting columns:', data.error);
                        console.log(data);

                        // Handle specific file not found errors
                        const errorMessage = data.error || 'Unknown error';
                        if (errorMessage.includes('not found') || errorMessage.includes('No such file')) {
                            showAlert('The uploaded file is no longer available. Please upload the file again.', 'warning');
                            // Reset the interface to allow re-upload

                            // Clear the context prompt text box
                            const fileContextTextarea = document.getElementById('fileContext');
                            if (fileContextTextarea) {
                                fileContextTextarea.value = '';
                            }

                            selectedFile = null;
                            currentSessionId = null;
                            translationCompleted = false;
                            fileInfo.style.display = 'none';
                            uploadArea.style.display = 'block';
                            fileOptions.style.display = 'none';
                            languageSettings.style.display = 'none';
                            fileInput.value = '';
                        } else {
                            showAlert('Error analyzing Excel file: ' + errorMessage, 'danger');
                            uploadArea.style.display = 'block';
                        }

                        loadingSpinner.style.display = 'none';
                        updateTranslateButton();
                    }
                })
                .catch(error => {
                    console.error('Get columns error:', error);
                    loadingSpinner.style.display = 'none';
                    uploadArea.style.display = 'block';
                    showAlert('Error analyzing Excel file', 'danger');
                });
        }

        function showFileOptions(fileExtension, options = null) {
            const fileOptionsHeader = document.getElementById('fileOptionsHeader');
            const fileOptionsDescription = document.getElementById('fileOptionsDescription');
            optionCheckboxes.innerHTML = '';
            let opts = [];
            if (fileExtension === '.xlsx') {
                fileOptionsHeader.textContent = 'Select columns to translate:';
                opts = options || [];
                toggleAllColumns.style.display = opts.length > 1 ? 'inline-block' : 'none';
            } else if (fileExtension === '.docx') {
                fileOptionsHeader.textContent = 'Document translation options:';
                fileOptionsDescription.textContent = 'Your Word document will be fully translated. You can preview sample paragraphs before starting the full translation.';
                opts = ['All Paragraphs'];
                toggleAllColumns.style.display = 'none';
            } else if (fileExtension === '.pptx') {
                fileOptionsHeader.textContent = 'Select slides to translate:';
                fileOptionsDescription.textContent = 'Select which slides you want to translate.';
                opts = ['All Slides'];
                toggleAllColumns.style.display = 'none';
            } else if (fileExtension == '.pdf') {
                fileOptionsHeader.textContent = 'Document translation options:';
                fileOptionsDescription.textContent = 'Your PDF will be converted to Word format and fully translated. You can preview sample paragraphs before starting the full translation.';
                opts = ['All Paragraphs'];
                toggleAllColumns.style.display = 'none';
            }
            const checkboxContainer = document.createElement('div');
            checkboxContainer.className = 'row';
            checkboxContainer.id = 'columnCheckboxes'; // Assign id for later reference
            opts.forEach((option, index) => {
                const colDiv = document.createElement('div');
                colDiv.className = 'col-md-6 column-checkbox';
                colDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${option}" id="opt${index}" checked>
                    <label class="form-check-label" for="opt${index}">
                        ${option}
                    </label>
                </div>
            `;
                checkboxContainer.appendChild(colDiv);
            });
            optionCheckboxes.appendChild(checkboxContainer);
            fileOptions.style.display = 'block';
            languageSettings.style.display = 'block';
            translateBtn.hidden = false;
        }

        function showTranslationPreview() {
            const selectedLangs = getSelectedTargetLanguages();
            if (!selectedFile || selectedLangs.length === 0) {
                alert('Please select a file and at least one target language');
                return;
            }

            let selectedColumns = [];

            // Handle different file types
            if (fileType === '.xlsx') {
                // Get selected columns for Excel files
                const checkboxContainer = document.getElementById('columnCheckboxes');
                if (!checkboxContainer) {
                    alert('No columns found');
                    return;
                }
                const checkboxes = checkboxContainer.querySelectorAll('input[type="checkbox"]:checked');
                selectedColumns = Array.from(checkboxes).map(cb => cb.value);

                if (selectedColumns.length === 0) {
                    alert('Please select at least one column to translate');
                    return;
                }
            } else if (fileType === '.docx' || fileType === '.pdf') {
                // For Word/PDF files, use 'paragraphs' as the column name
                selectedColumns = ['paragraphs'];
            } else if (fileType === '.pptx') {
                // For PowerPoint files, use 'slides' as the column name
                selectedColumns = ['slides'];
            } else {
                alert('Preview is not available for this file type');
                return;
            }

            // Preview all selected columns for all target languages
            const targetLanguagesToPreview = selectedLangs.map(lang => getLanguageName(lang));

            // Show loading state for both preview and new preview buttons, and disable translate button
            previewBtn.disabled = true;
            previewBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Preview...';
            newPreviewBtn.disabled = true;
            newPreviewBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Preview...';
            translateBtn.disabled = true;

            // Call preview API with all selected columns and languages
            const fileContextElement = document.getElementById('fileContext');
            const fileContext = fileContextElement ? fileContextElement.value : '';
            fetch('/translator/api/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    column_names: selectedColumns,  // Send all selected columns
                    target_languages: targetLanguagesToPreview,  // Send all target languages
                    file_context: fileContext
                })
            })
                .then(response => response.json())
                .then(data => {
                    // Reset button states
                    previewBtn.disabled = false;
                    previewBtn.innerHTML = '<i class="fas fa-eye me-2"></i>Preview Translation';
                    newPreviewBtn.disabled = false;
                    newPreviewBtn.innerHTML = '<i class="fas fa-redo me-2"></i>New Preview';
                    translateBtn.disabled = false;

                    console.log('Preview API response:', data);

                    if (data.success) {
                        // Explicit file type checks - force Excel/PowerPoint to use table preview
                        const isExcelFile = fileType === '.xlsx';
                        const isPowerPointFile = fileType === '.pptx';
                        const isWordFile = fileType === '.docx';
                        const isPdfFile = fileType === '.pdf';
                        const isDocumentFile = isWordFile || isPdfFile;

                        // Force Excel and PowerPoint files to always use table preview
                        const shouldShowDocumentPreview = data.use_document_preview && isDocumentFile;;
                        const shouldShowPPTPreview = data.use_document_preview && isPowerPointFile;
                        const shouldShowTablePreview = !data.use_document_preview || isExcelFile;
                        console.log('File type:', fileType, 'Is Excel:', isExcelFile, 'Is PowerPoint:', isPowerPointFile, 'Is Word:', isWordFile, 'Is PDF:', isPdfFile);
                        console.log('Server wants document preview:', data.use_document_preview, 'Should show document preview:', shouldShowDocumentPreview);

                        if (shouldShowDocumentPreview) {
                            // Handle Word/PDF document preview
                            console.log('Displaying Word document preview with data:', data);

                            // Hide New Preview button for non-Excel files
                            const newPreviewBtn = document.getElementById('newPreviewBtn');
                            if (newPreviewBtn) {
                                newPreviewBtn.style.display = 'none';
                            }

                            // Add debugging for the document URL
                            if (data.preview_document_url) {
                                console.log('Preview document URL:', data.preview_document_url);

                                // Let's also try to fetch and log some info about the document
                                fetch(data.preview_document_url)
                                    .then(response => {
                                        console.log('Document fetch response:', response.status, response.headers.get('content-length'));
                                        return response.blob();
                                    })
                                    .then(blob => {
                                        console.log('Document blob size:', blob.size, 'bytes');
                                    })
                                    .catch(err => console.error('Error fetching document:', err));
                            }

                            displayWordDocumentPreview(data);
                        } else if (shouldShowTablePreview) {
                            // Handle Excel/PowerPoint table preview or fallback for mismatched server response
                            if (data.use_document_preview && !shouldShowDocumentPreview) {
                                console.warn('Server wants to show document preview but client override forces table preview for file type:', fileType);
                            }
                            console.log('Using table preview for file type:', fileType);
                            displayPreview(data);

                            // Show New Preview button for Excel files only
                            const newPreviewBtn = document.getElementById('newPreviewBtn');
                            if (newPreviewBtn && isExcelFile) {
                                newPreviewBtn.style.display = 'inline-block';
                            } else if (newPreviewBtn) {
                                newPreviewBtn.style.display = 'none';
                            }
                        } else if (shouldShowPPTPreview) {
                            // Handle Word/PDF document preview
                            console.log('Displaying PPT document preview with data:', data);

                            // Hide New Preview button for non-Excel files
                            const newPreviewBtn = document.getElementById('newPreviewBtn');
                            if (newPreviewBtn) {
                                newPreviewBtn.style.display = 'none';
                            }

                            // Add debugging for the document URL
                            if (data.preview_document_url) {
                                console.log('Preview document URL:', data.preview_document_url);

                                // Let's also try to fetch and log some info about the document
                                fetch(data.preview_document_url)
                                    .then(response => {
                                        console.log('Document fetch response:', response.status, response.headers.get('content-length'));
                                        return response.blob();
                                    })
                                    .then(blob => {
                                        console.log('Document blob size:', blob.size, 'bytes');
                                    })
                                    .catch(err => console.error('Error fetching document:', err));
                            }

                            displayPPTDocumentPreview(data);
                        }
                    } else {
                        alert('Preview failed: ' + data.error);
                    }
                })
                .catch(error => {
                    console.error('Preview error:', error);
                    // Reset button states on error
                    previewBtn.disabled = false;
                    previewBtn.innerHTML = '<i class="fas fa-eye me-2"></i>Preview Translation';
                    newPreviewBtn.disabled = false;
                    newPreviewBtn.innerHTML = '<i class="fas fa-redo me-2"></i>New Preview';
                    translateBtn.disabled = false;
                    alert('Preview failed: ' + error.message);
                });
        }

        function displayPreview(data) {
            console.log('Displaying preview with data:', data);
            console.log('Current fileType:', fileType);
            previewData = data;

            // Hide any existing Word document preview container
            const wordPreviewContainer = document.getElementById('wordDocumentPreviewContainer');
            if (wordPreviewContainer) {
                wordPreviewContainer.style.display = 'none';
                console.log('Hidden existing Word preview container');
            }

            // Show the table preview
            const previewTable = document.getElementById('previewTable');
            if (previewTable) {
                previewTable.style.display = 'table';
                console.log('Showing table preview');
            }

            // Update preview section content
            const columnNames = data.column_names || [data.column_name];
            const columnCount = columnNames.length;
            const targetLanguages = data.target_languages || [data.target_language];

            // Update description based on file type
            const previewDescription = document.getElementById('previewDescription');
            if (previewDescription) {
                if (fileType === '.docx' || fileType === '.pdf') {
                    previewDescription.innerHTML = `Preview of the translation in <span id="previewTargetLanguage">${targetLanguages.join(', ')}</span> of sample paragraphs from the document:`;
                } else {
                    previewDescription.innerHTML = `Preview of the translation in <span id="previewTargetLanguage">${targetLanguages.join(', ')}</span> of random cells taken from the <span id="previewColumnCount">${columnCount}</span> selected column(s):`;
                }
            }

            // Update table headers for multiple languages
            const tableHead = document.getElementById('previewTableHead');
            const headerRow = tableHead.querySelector('tr');

            // Clear existing headers
            headerRow.innerHTML = '';

            // Add fixed columns with appropriate headers based on file type
            if (fileType === '.docx' || fileType === '.pdf') {
                headerRow.innerHTML += '<th style="width: 8%;">Paragraph</th>';
                headerRow.innerHTML += '<th style="width: 15%;">Section</th>';
                headerRow.innerHTML += '<th style="width: 25%;">Original</th>';
            } else {
                headerRow.innerHTML += '<th style="width: 8%;">Row</th>';
                headerRow.innerHTML += '<th style="width: 15%;">Column</th>';
                headerRow.innerHTML += '<th style="width: 25%;">Original</th>';
            }

            // Add dynamic language columns
            const languageColumnWidth = Math.floor(52 / targetLanguages.length); // Distribute remaining 52% among languages
            targetLanguages.forEach(language => {
                headerRow.innerHTML += `<th style="width: ${languageColumnWidth}%;">${escapeHtml(language)}</th>`;
            });

            // Populate preview table
            const tableBody = document.getElementById('previewTableBody');
            tableBody.innerHTML = '';

            console.log('Preview data array:', data.preview_data);
            console.log('Preview data length:', data.preview_data ? data.preview_data.length : 'undefined');

            if (data.preview_data && data.preview_data.length > 0) {
                // Group data by column to add visual separation
                const groupedData = {};
                data.preview_data.forEach(row => {
                    if (!groupedData[row.column]) {
                        groupedData[row.column] = [];
                    }
                    groupedData[row.column].push(row);
                });

                Object.keys(groupedData).forEach((columnName, columnIndex) => {
                    // Add a separator row between columns (except for the first column)
                    if (columnIndex > 0) {
                        const separatorTr = document.createElement('tr');
                        separatorTr.className = 'column-separator';
                        const totalColumns = 3 + targetLanguages.length; // Row + Column + Original + Languages
                        separatorTr.innerHTML = `
                        <td colspan="${totalColumns}" class="text-center text-muted py-2" style="background-color: #f8f9fa; border-top: 2px solid #dee2e6; font-weight: 500;">
                        </td>
                    `;
                        tableBody.appendChild(separatorTr);
                    }

                    // Add rows for this column - use actual Excel row numbers
                    groupedData[columnName].forEach((row, rowIndex) => {
                        console.log(`Processing Excel row ${row.row} for column ${columnName}:`, row);
                        const tr = document.createElement('tr');

                        // Build row HTML with dynamic language columns
                        let rowHTML = `
                        <td class="fw-bold">${row.row}</td>
                        <td class="text-primary fw-semibold">${escapeHtml(row.column || 'N/A')}</td>
                        <td>${escapeHtml(row.original)}</td>
                    `;

                        // Add translation columns for each language
                        targetLanguages.forEach(language => {
                            const translation = row.translations && row.translations[language]
                                ? row.translations[language]
                                : row.translated || `[Translating to ${language}...]`;
                            rowHTML += `<td class="text-success">${escapeHtml(translation)}</td>`;
                        });

                        tr.innerHTML = rowHTML;
                        tableBody.appendChild(tr);
                    });
                });
            } else {
                // Add a message if no data
                const tr = document.createElement('tr');
                tr.innerHTML = `
                <td colspan="4" class="text-center text-muted">No preview data available</td>
            `;
                tableBody.appendChild(tr);
            }

            // Show preview section and hide translate button section
            previewSection.style.display = 'block';
            const translateButtonSection = document.getElementById('translateButtonSection');
            if (translateButtonSection) {
                translateButtonSection.style.display = 'none';
            }

            // Scroll to preview section
            previewSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        function displayWordDocumentPreview(data) {
            console.log('=== DISPLAYING WORD DOCUMENT PREVIEW ===');
            console.log('Displaying Word document preview with data:', data);

            const targetLanguages = data.target_languages || [];
            console.log('Target languages:', targetLanguages);

            // Update preview section content for Word documents - check if elements exist first
            const columnCountElement = document.getElementById('previewColumnCount');
            const targetLanguageElement = document.getElementById('previewTargetLanguage');
            console.log('Found preview elements - columnCount:', !!columnCountElement, 'targetLanguage:', !!targetLanguageElement);

            if (columnCountElement) {
                columnCountElement.textContent = '1';
            }
            if (targetLanguageElement) {
                targetLanguageElement.textContent = targetLanguages.join(', ');
            }

            // Update description for Word documents
            const previewDescription = document.getElementById('previewDescription');
            if (previewDescription) {
                previewDescription.innerHTML = `Preview of sample paragraphs translated to ${targetLanguages.join(', ')}:`;
            }

            // Hide the table and show Word preview container
            const previewTable = document.getElementById('previewTable');
            console.log('Found previewTable:', !!previewTable);
            if (previewTable) {
                previewTable.style.display = 'none';
                console.log('Hidden preview table');
            }

            // Create Word preview container if it doesn't exist
            let wordPreviewContainer = document.getElementById('wordDocumentPreviewContainer');
            console.log('Existing wordPreviewContainer:', !!wordPreviewContainer);
            if (!wordPreviewContainer) {
                console.log('Creating new wordPreviewContainer');
                wordPreviewContainer = document.createElement('div');
                wordPreviewContainer.id = 'wordDocumentPreviewContainer';
                wordPreviewContainer.className = 'word-preview-container';
                wordPreviewContainer.innerHTML = `
                <div class="word-preview-loading" id="wordDocumentPreviewLoading" style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 180px; width: 100%; background: transparent !important; border: none; box-shadow: none;">
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; width: 100%; background: transparent !important;">
                        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem; margin: 0 auto; background: transparent !important;"></div>
                        <p class="mt-3" style="text-align: center; width: 100%; background: transparent !important;">Creating document preview...</p>
                    </div>
                </div>
                <div id="wordDocumentPreviewContent" class="word-preview-content" style="display: none;">
                    <!-- Word document preview will be rendered here -->
                </div>
                <div class="mt-3">
                    <label for="wordPreviewLanguageSelect" class="form-label fw-semibold">Preview Language:</label>
                    <select class="form-select form-select-sm d-inline-block" id="wordPreviewLanguageSelect" style="width: auto; margin-left: 10px; background: transparent !important;">
                        <!-- Language options will be populated -->
                    </select>
                    <div class="language-selector-decoration"></div>
                </div>
            `;

                // Insert after the table container
                const tableContainer = document.querySelector('.preview-table-container');
                console.log('Found tableContainer:', !!tableContainer);
                if (tableContainer) {
                    tableContainer.parentNode.insertBefore(wordPreviewContainer, tableContainer.nextSibling);
                    console.log('Inserted wordPreviewContainer after tableContainer');
                } else {
                    console.error('No table container found to insert word preview after');
                }
            }

            // Populate language dropdown
            const languageSelect = document.getElementById('wordPreviewLanguageSelect');
            if (languageSelect) {
                languageSelect.innerHTML = '';
                targetLanguages.forEach(lang => {
                    const option = document.createElement('option');
                    option.value = lang;
                    option.textContent = getLanguageName(lang);
                    languageSelect.appendChild(option);
                });

                // Add event listener for language change
                languageSelect.addEventListener('change', loadWordDocumentPreview);
            }

            // Show the preview section and load the first language
            const previewSection = document.getElementById('previewSection');
            const translateButtonSection = document.getElementById('translateButtonSection');
            console.log('Found sections - preview:', !!previewSection, 'translateButton:', !!translateButtonSection);

            if (previewSection) {
                previewSection.style.display = 'block';
                console.log('Showed preview section');
            } else {
                console.error('previewSection element not found!');
            }
            if (translateButtonSection) {
                translateButtonSection.style.display = 'none';
                console.log('Hidden translate button section');
            }

            // Make sure the word preview container is visible
            if (wordPreviewContainer) {
                wordPreviewContainer.style.display = 'block';
                console.log('Made word preview container visible');
            }

            // Load preview for the first language
            console.log('About to load word document preview');
            loadWordDocumentPreview();

            // Scroll to preview section
            if (previewSection) {
                previewSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        function loadWordDocumentPreview() {
            console.log('=== LOADING WORD DOCUMENT PREVIEW ===');
            const languageSelect = document.getElementById('wordPreviewLanguageSelect');
            if (!languageSelect) {
                console.error('Language select element not found');
                return;
            }

            const selectedLanguage = languageSelect.value;
            console.log('Selected language:', selectedLanguage);
            if (!selectedLanguage) {
                console.error('No language selected');
                return;
            }

            const loadingElement = document.getElementById('wordDocumentPreviewLoading');
            const contentElement = document.getElementById('wordDocumentPreviewContent');
            console.log('Found preview elements - loading:', !!loadingElement, 'content:', !!contentElement);

            if (!loadingElement || !contentElement) {
                console.error('Preview elements not found');
                return;
            }

            console.log('Showing loading state');
            loadingElement.style.display = 'block';
            contentElement.innerHTML = '';

            // Get file context
            const fileContextElement = document.getElementById('fileContext');
            const fileContext = fileContextElement ? fileContextElement.value : '';
            console.log('File context:', fileContext);

            // Create preview document with cache-busting
            const timestamp = Date.now();
            console.log('Making request to preview API with timestamp:', timestamp);
            fetch('/translator/api/preview-word-document?t=' + timestamp, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify({
                    target_language: selectedLanguage,
                    file_context: fileContext,
                    timestamp: timestamp
                })
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to create preview document');
                    }
                    return response.arrayBuffer();
                })
                .then(arrayBuffer => {
                    // Use docx-preview to render the document
                    console.log('Available global objects:', Object.keys(window));
                    console.log('docx object:', window.docx);
                    console.log('Received arrayBuffer size:', arrayBuffer.byteLength, 'bytes');

                    contentElement.style.display = 'block';

                    // Check for different possible global names (CDN version uses window.docx)
                    const docxPreview = window.docx;

                    if (docxPreview && typeof docxPreview.renderAsync === 'function') {
                        console.log('Using docx.renderAsync');
                        return docxPreview.renderAsync(arrayBuffer, contentElement, null, {
                            className: "docx-preview",
                            inWrapper: true,
                            ignoreWidth: false,
                            ignoreHeight: false,
                            ignoreFonts: false,
                            breakPages: true,
                            ignoreLastRenderedPageBreak: true,
                            experimental: false,
                            trimXmlDeclaration: true,
                            useBase64URL: false,
                            useMathMLPolyfill: false,
                            renderChanges: false,
                            renderHeaders: true,
                            renderFooters: true,
                            renderFootnotes: true,
                            renderEndnotes: true
                        });
                    } else {
                        console.error('docx-preview library not found or invalid. Available:', docxPreview);
                        console.error('window.docx type:', typeof window.docx);
                        console.error('window.docx methods:', window.docx ? Object.keys(window.docx) : 'undefined');
                        throw new Error('docx-preview library not loaded correctly. Please refresh the page and try again.');
                    }
                })
                .then(() => {
                    loadingElement.style.display = 'none';
                    showAlert(`Document preview loaded successfully for ${getLanguageName(selectedLanguage)}!`, 'success');
                })
                .catch(error => {
                    console.error('Word document preview error:', error);
                    loadingElement.style.display = 'none';
                    contentElement.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Unable to load document preview: ${error.message}
                </div>
            `;
                    showAlert('Failed to load document preview', 'warning');
                });
        }

        function displayPPTDocumentPreview(data) {
            console.log('=== DISPLAYING POWERPOINT DOCUMENT PREVIEW (HTML) ===');
            console.log('Displaying PowerPoint document preview with data:', data);

            const targetLanguages = data.target_languages || [];
            console.log('Target languages:', targetLanguages);

            // Update preview section content for PowerPoint documents - check if elements exist first
            const columnCountElement = document.getElementById('previewColumnCount');
            const targetLanguageElement = document.getElementById('previewTargetLanguage');
            console.log('Found preview elements - columnCount:', !!columnCountElement, 'targetLanguage:', !!targetLanguageElement);

            if (columnCountElement) {
                columnCountElement.textContent = '1';
            }
            if (targetLanguageElement) {
                targetLanguageElement.textContent = targetLanguages.join(', ');
            }

            // Update description for PowerPoint documents
            const previewDescription = document.getElementById('previewDescription');
            if (previewDescription) {
                previewDescription.innerHTML = `Preview of sample slides translated to ${targetLanguages.join(', ')}:`;
            }

            // Hide the table and show PowerPoint preview container
            const previewTable = document.getElementById('previewTable');
            console.log('Found previewTable:', !!previewTable);
            if (previewTable) {
                previewTable.style.display = 'none';
                console.log('Hidden preview table');
            }

            // Create PowerPoint preview container if it doesn't exist
            let pptPreviewContainer = document.getElementById('pptDocumentPreviewContainer');
            console.log('Existing pptPreviewContainer:', !!pptPreviewContainer);
            if (!pptPreviewContainer) {
                console.log('Creating new pptPreviewContainer');
                pptPreviewContainer = document.createElement('div');
                pptPreviewContainer.id = 'pptDocumentPreviewContainer';
                pptPreviewContainer.className = 'ppt-preview-container';
                pptPreviewContainer.innerHTML = `
            <div class="ppt-preview-loading" id="pptDocumentPreviewLoading" style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 180px; width: 100%; background: transparent !important; border: none; box-shadow: none;">
                <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; width: 100%; background: transparent !important;">
                    <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem; margin: 0 auto; background: transparent !important;"></div>
                    <p class="mt-3" style="text-align: center; width: 100%; background: transparent !important;">Creating PowerPoint preview...</p>
                </div>
            </div>
            <div id="pptDocumentPreviewContent" class="ppt-preview-content" style="display: none; border: 1px solid #dee2e6; border-radius: 8px; background: white; max-height: 600px; overflow-y: auto;">
                <!-- PowerPoint document preview HTML will be rendered here -->
            </div>
            <div class="mt-3">
                <label for="pptPreviewLanguageSelect" class="form-label fw-semibold">Preview Language:</label>
                <select class="form-select form-select-sm d-inline-block" id="pptPreviewLanguageSelect" style="width: auto; margin-left: 10px; background: transparent !important;">
                    <!-- Language options will be populated -->
                </select>
                <div class="language-selector-decoration"></div>
            </div>
        `;

                // Insert after the table container
                const tableContainer = document.querySelector('.preview-table-container');
                console.log('Found tableContainer:', !!tableContainer);
                if (tableContainer) {
                    tableContainer.parentNode.insertBefore(pptPreviewContainer, tableContainer.nextSibling);
                    console.log('Inserted pptPreviewContainer after tableContainer');
                } else {
                    console.error('No table container found to insert PowerPoint preview after');
                }
            }

            // Populate language dropdown
            const languageSelect = document.getElementById('pptPreviewLanguageSelect');
            if (languageSelect) {
                languageSelect.innerHTML = '';
                targetLanguages.forEach(lang => {
                    const option = document.createElement('option');
                    option.value = lang;
                    option.textContent = getLanguageName(lang);
                    languageSelect.appendChild(option);
                });

                // Add event listener for language change
                languageSelect.addEventListener('change', loadPPTDocumentPreview);
            }

            // Show the preview section and load the first language
            const previewSection = document.getElementById('previewSection');
            const translateButtonSection = document.getElementById('translateButtonSection');
            console.log('Found sections - preview:', !!previewSection, 'translateButton:', !!translateButtonSection);

            if (previewSection) {
                previewSection.style.display = 'block';
                console.log('Showed preview section');
            } else {
                console.error('previewSection element not found!');
            }
            if (translateButtonSection) {
                translateButtonSection.style.display = 'none';
                console.log('Hidden translate button section');
            }

            // Make sure the PowerPoint preview container is visible
            if (pptPreviewContainer) {
                pptPreviewContainer.style.display = 'block';
                console.log('Made PowerPoint preview container visible');
            }

            // Load preview for the first language
            console.log('About to load PowerPoint document preview');
            loadPPTDocumentPreview();

            // Scroll to preview section
            if (previewSection) {
                previewSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }

        function loadPPTDocumentPreview() {
            console.log('=== LOADING POWERPOINT DOCUMENT PREVIEW (HTML) ===');
            const languageSelect = document.getElementById('pptPreviewLanguageSelect');
            if (!languageSelect) {
                console.error('Language select element not found');
                return;
            }

            const selectedLanguage = languageSelect.value;
            console.log('Selected language:', selectedLanguage);
            if (!selectedLanguage) {
                console.error('No language selected');
                return;
            }

            const loadingElement = document.getElementById('pptDocumentPreviewLoading');
            const contentElement = document.getElementById('pptDocumentPreviewContent');
            console.log('Found preview elements - loading:', !!loadingElement, 'content:', !!contentElement);

            if (!loadingElement || !contentElement) {
                console.error('Preview elements not found');
                return;
            }

            console.log('Showing loading state');
            loadingElement.style.display = 'block';
            contentElement.style.display = 'none';
            contentElement.innerHTML = '';

            // Get file context
            const fileContextElement = document.getElementById('fileContext');
            const fileContext = fileContextElement ? fileContextElement.value : '';
            console.log('File context:', fileContext);

            // Create preview document with cache-bustingpreview_ppt_path
            const timestamp = Date.now();
            console.log('Making request to preview API with timestamp:', timestamp);
            fetch('/translator/api/preview-powerpoint-document', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                body: JSON.stringify({
                    target_language: selectedLanguage,
                    file_context: fileContext,
                    timestamp: timestamp
                })
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to create PowerPoint HTML preview');
                    }
                    return response.json(); // Cambiato da arrayBuffer() a text() per HTML
                })
                .then(jsonResponse => {
                    console.log('Received JSON response:', jsonResponse);

                    if (jsonResponse.preview_ppt_path && typeof jsonResponse.preview_ppt_path === 'string') {
                        // La risposta contiene HTML nella chiave preview_ppt_path
                        const htmlContent = jsonResponse.preview_ppt_path;
                        console.log('Received HTML content, length:', htmlContent.length);

                        // Pulisci e prepara l'HTML
                        const cleanedHtml = sanitizeAndPrepareHtml(htmlContent);

                        // Inserisci l'HTML nel contenitore
                        contentElement.innerHTML = cleanedHtml;

                        // Applica stili aggiuntivi se necessario
                        applyPPTPreviewStyles(contentElement);

                    } else {
                        throw new Error('Invalid response format: missing or invalid preview_ppt_path');
                    }

                    // Mostra il contenuto
                    loadingElement.style.display = 'none';
                    contentElement.style.display = 'block';

                    showAlert(`PowerPoint preview loaded successfully for ${getLanguageName(selectedLanguage)}!`, 'success');
                })
                .catch(error => {
                    console.error('PowerPoint HTML document preview error:', error);
                    loadingElement.style.display = 'none';
                    contentElement.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Unable to load PowerPoint preview: ${error.message}
            </div>
        `;
                    contentElement.style.display = 'block';
                    showAlert('Failed to load PowerPoint preview', 'warning');
                });
        }

        // Funzione per pulire e preparare l'HTML del PowerPoint
        function sanitizeAndPrepareHtml(htmlContent) {
            console.log('Sanitizing and preparing HTML content');

            // Crea un parser DOM temporaneo
            const parser = new DOMParser();
            const doc = parser.parseFromString(htmlContent, 'text/html');

            // Rimuovi script potenzialmente pericolosi
            const scripts = doc.querySelectorAll('script');
            scripts.forEach(script => script.remove());

            // Assicurati che le immagini abbiano percorsi relativi corretti
            const images = doc.querySelectorAll('img');
            images.forEach(img => {
                const src = img.getAttribute('src');
                if (src && !src.startsWith('http') && !src.startsWith('data:')) {
                    // Aggiungi il prefisso del percorso dell'API se necessario
                    img.setAttribute('src', `/translator/api/ppt-assets/${src}`);
                }
            });

            // Assicurati che i CSS abbiano percorsi corretti
            const links = doc.querySelectorAll('link[rel="stylesheet"]');
            links.forEach(link => {
                const href = link.getAttribute('href');
                if (href && !href.startsWith('http') && !href.startsWith('/')) {
                    link.setAttribute('href', `/translator/api/ppt-assets/${href}`);
                }
            });

            // Restituisci solo il contenuto del body se presente, altrimenti tutto
            const body = doc.querySelector('body');
            return body ? body.innerHTML : doc.documentElement.innerHTML;
        }

        // Funzione per applicare stili aggiuntivi al preview PowerPoint
        function applyPPTPreviewStyles(container) {
            console.log('Applying additional PPT preview styles');

            // Aggiungi stili CSS per migliorare la visualizzazione
            const style = document.createElement('style');
            style.textContent = `
        .ppt-preview-content {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .ppt-preview-content .slide {
            margin-bottom: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            background: white;
            overflow: hidden;
        }
        
        .ppt-preview-content .slide-header {
            background: #f8f9fa;
            padding: 8px 16px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
            font-size: 14px;
            color: #495057;
        }
        
        .ppt-preview-content .slide-content {
            padding: 16px;
            min-height: 200px;
        }
        
        .ppt-preview-content img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }
        
        .ppt-preview-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .ppt-preview-content table td,
        .ppt-preview-content table th {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        
        .ppt-preview-content table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    `;

            // Aggiungi gli stili se non esistono già
            if (!document.querySelector('#ppt-preview-styles')) {
                style.id = 'ppt-preview-styles';
                document.head.appendChild(style);
            }
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function getLanguageName(code) {
            const languages = {
                'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
                'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
                'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
                'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
                'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
            };
            return languages[code] || code;
        }

        function getSelectedTargetLanguages() {
            // Returns an array of selected language codes
            return selectedTargetLanguages.slice(); // Return a copy of the array
        }

        let translationCompleted = false; // Track if translation was completed

        function updateTranslateButton() {
            const hasFile = selectedFile !== null;
            const selectedLangs = getSelectedTargetLanguages();

            // Hide buttons if translation was completed and no changes made
            if (translationCompleted) {
                translateBtn.style.display = 'none';
                previewBtn.style.display = 'none';
                return;
            }

            // Show/hide translate button section
            const translateButtonSection = document.getElementById('translateButtonSection');
            if (translateButtonSection) {
                translateButtonSection.style.display = hasFile ? 'block' : 'none';
            }

            // Update translate button - Reset style.display first to clear any previous hiding
            translateBtn.style.display = '';
            translateBtn.hidden = !(hasFile);
            translateBtn.disabled = !(hasFile && selectedLangs.length > 0);

            // Update preview button (show for Excel files with columns selected OR Word/PDF files)
            const hasColumns = document.getElementById('columnCheckboxes') &&
                document.querySelectorAll('#columnCheckboxes input[type="checkbox"]:checked').length > 0;
            const isExcelFile = fileType === '.xlsx';
            const isWordOrPdfFile = fileType === '.docx' || fileType === '.pdf';
            const isPowerPointFile = fileType === '.pptx';

            const showPreview = hasFile && selectedLangs.length > 0 &&
                ((isExcelFile && hasColumns) || isWordOrPdfFile || isPowerPointFile);

            // Reset style.display first to clear any previous hiding
            previewBtn.style.display = showPreview ? 'inline-block' : 'none';
            previewBtn.disabled = !showPreview;

            // Hide preview section when settings change
            if (previewSection.style.display === 'block') {
                previewSection.style.display = 'none';
                const translateButtonSection = document.getElementById('translateButtonSection');
                if (translateButtonSection) {
                    translateButtonSection.style.display = 'block';
                }
            }
        }

        function showTranslateButtonAfterChange() {
            // Show translate button again when user makes changes
            if (translationCompleted) {
                translationCompleted = false;
                updateTranslateButton();
            }

            // Hide preview section when settings change
            if (previewSection.style.display === 'block') {
                previewSection.style.display = 'none';
                const translateButtonSection = document.getElementById('translateButtonSection');
                if (translateButtonSection) {
                    translateButtonSection.style.display = 'block';
                }
            }
        }

        function startTranslation() {
            const selectedLangs = getSelectedTargetLanguages();
            if (!selectedFile || selectedLangs.length === 0) {
                alert('Please select a file and at least one target language');
                return;
            }

            // Remove any existing download links immediately when translation starts
            const existingLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
            existingLinks.forEach(link => link.remove());

            // Get selected columns for Excel files
            let selectedColumns = [];
            if (fileType === '.xlsx') {
                const checkboxes = document.getElementById('columnCheckboxes').querySelectorAll('input[type="checkbox"]:checked');
                selectedColumns = Array.from(checkboxes).map(cb => cb.value);
            } else if (fileType === '.docx') {
                selectedColumns = ['All Paragraphs'];
            } else if (fileType === '.pptx') {
                selectedColumns = ['All Slides'];
            } else if (fileType === '.pdf') {
                selectedColumns = ['All Paragraphs'];
            }

            translateBtn.disabled = true;
            translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Translating...';

            // Also disable and update the continue translation button if it exists
            continueTranslationBtn.disabled = true;
            continueTranslationBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Translating...';

            // Disable the remove file button during translation
            removeFile.disabled = true;
            removeFile.style.opacity = '0.5';
            removeFile.style.cursor = 'not-allowed';

            // Generate session ID for tracking
            const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            currentSessionId = sessionId; // Store the session ID

            // Start translation
            const fileContextElement = document.getElementById('fileContext');
            const fileContext = fileContextElement ? fileContextElement.value : '';
            const sourceLanguageElement = document.getElementById('sourceLanguage');
            const translationData = {
                target_languages: selectedLangs,
                source_language: sourceLanguageElement ? sourceLanguageElement.value : '',
                selected_columns: selectedColumns,
                file_type: '.' + selectedFile.name.split('.').pop().toLowerCase(),
                original_filename: selectedFile.name,
                file_context: fileContext,
                session_id: sessionId
            };

            // Start the translation request
            fetch('/translator/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(translationData)
            })
                .then(response => response.json())
                .then(data => {
                    console.log('Translation response received:', data); // Debug log

                    translateBtn.disabled = false;
                    translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Start Translation';

                    // Also reset the continue translation button
                    continueTranslationBtn.disabled = false;
                    continueTranslationBtn.innerHTML = '<i class="fas fa-check me-2"></i>Continue with Full Translation';

                    // Re-enable the remove file button after translation
                    removeFile.disabled = false;
                    removeFile.style.opacity = '1';
                    removeFile.style.cursor = 'pointer';

                    if (data.success) {
                        // Mark translation as completed IMMEDIATELY to prevent cleanup
                        translationCompleted = true;
                        console.log('Translation marked as completed, preventing cleanup');

                        translateBtn.style.display = 'none';

                        // Show success message with information about partial failures if applicable
                        const requestedLangs = getSelectedTargetLanguages();
                        const successfulLangs = data.languages || [];

                        if (successfulLangs.length === requestedLangs.length) {
                            showAlert('Translation completed successfully!', 'success');
                        } else if (successfulLangs.length > 0) {
                            const failedLangs = requestedLangs.filter(lang => !successfulLangs.includes(lang));
                            const failedLangNames = failedLangs.map(code => getLanguageName(code)).join(', ');
                            showAlert(`Translation partially completed. ${failedLangNames} translation(s) failed, but others succeeded.`, 'warning');
                        } else {
                            showAlert('Translation failed for all languages.', 'danger');
                            return;
                        }

                        // Create download link only if we have successful translations
                        if (successfulLangs.length > 0) {
                            console.log('Creating download link for successful languages:', successfulLangs);

                            const downloadLink = document.createElement('a');
                            downloadLink.className = 'btn btn-success btn-lg mt-3 download-link-custom';
                            downloadLink.style.display = 'block';
                            downloadLink.style.margin = '20px auto';
                            downloadLink.style.width = 'fit-content';

                            if (data.zip_file && successfulLangs.length > 1) {
                                // Multiple successful languages - show zip download
                                const zipFileParam = encodeURIComponent(data.zip_file);
                                downloadLink.href = `/translator/api/download/current?zip_file=${zipFileParam}`;
                                downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download All Translations (ZIP)';
                            } else if (successfulLangs.length === 1) {
                                // Single successful language - show single file download
                                const langParam = encodeURIComponent(successfulLangs[0]);
                                downloadLink.href = `/translator/api/download/current?lang=${langParam}`;
                                downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                            } else {
                                // Multiple languages but no zip (shouldn't happen, but fallback)
                                const langParam = encodeURIComponent(successfulLangs[0]);
                                downloadLink.href = `/translator/api/download/current?lang=${langParam}`;
                                downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                            }

                            // Insert download link in the appropriate location
                            // Check which button section is currently visible and insert accordingly
                            const translateBtnElement = document.getElementById('translateBtn');
                            const continueTranslationBtnElement = document.getElementById('continueTranslationBtn');
                            const previewSection = document.getElementById('previewSection');
                            const translateButtonSection = document.getElementById('translateButtonSection');

                            // Determine the best place to insert the download link
                            let insertionParent;
                            let insertionPoint;

                            if (previewSection && previewSection.style.display !== 'none') {
                                // Preview is showing, insert after preview section
                                insertionParent = previewSection.parentNode;
                                insertionPoint = previewSection.nextSibling;
                            } else if (translateButtonSection && translateButtonSection.style.display !== 'none') {
                                // Normal translation button section is showing
                                insertionParent = translateBtnElement.parentNode;
                                insertionPoint = translateBtnElement.nextSibling;
                            } else {
                                // Fallback: insert after translate button
                                insertionParent = translateBtnElement.parentNode;
                                insertionPoint = translateBtnElement.nextSibling;
                            }

                            // Create a container for the download section to ensure proper positioning
                            const downloadSection = document.createElement('div');
                            downloadSection.className = 'text-center mt-4';
                            downloadSection.appendChild(downloadLink);

                            insertionParent.insertBefore(downloadSection, insertionPoint);

                            console.log('Download link inserted successfully into:', insertionParent, 'at position:', insertionPoint);

                            // Add a "Translate Another File" button
                            const newTranslationBtn = document.createElement('button');
                            newTranslationBtn.className = 'btn btn-outline-primary btn-lg mt-3 new-translation-btn';
                            newTranslationBtn.style.display = 'block';
                            newTranslationBtn.style.margin = '10px auto 0';
                            newTranslationBtn.style.width = 'fit-content';
                            newTranslationBtn.innerHTML = '<i class="fas fa-plus me-2"></i>Translate Another File';
                            newTranslationBtn.addEventListener('click', function () {
                                // Reset UI for new translation - cleanup handled by server on next upload

                                // Clear the context prompt text box
                                const fileContextTextarea = document.getElementById('fileContext');
                                if (fileContextTextarea) {
                                    fileContextTextarea.value = '';
                                }

                                selectedFile = null;
                                currentSessionId = null;
                                translationCompleted = false;
                                fileInfo.style.display = 'none';
                                uploadArea.style.display = 'block';
                                fileOptions.style.display = 'none';
                                languageSettings.style.display = 'none';
                                translatedWordPreviewLoaded = {};
                                fileInput.value = '';
                                // Remove all custom buttons and download links
                                const existingDownloadLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
                                existingDownloadLinks.forEach(link => link.remove());
                                updateTranslateButton();
                            });

                            // Add the new translation button to the same download section
                            downloadSection.appendChild(newTranslationBtn);
                        }
                    } else {
                        // Handle specific file not found errors
                        const errorMessage = data.error || 'Unknown error';
                        if (errorMessage.includes('Package not found') || errorMessage.includes('file not found') || errorMessage.includes('No such file')) {
                            showAlert('The uploaded file is no longer available. Please upload the file again.', 'warning');
                            // Reset the interface to allow re-upload

                            // Clear the context prompt text box
                            const fileContextTextarea = document.getElementById('fileContext');
                            if (fileContextTextarea) {
                                fileContextTextarea.value = '';
                            }

                            selectedFile = null;
                            currentSessionId = null;
                            translationCompleted = false;
                            fileInfo.style.display = 'none';
                            uploadArea.style.display = 'block';
                            fileOptions.style.display = 'none';
                            languageSettings.style.display = 'none';
                            fileInput.value = '';
                            updateTranslateButton();
                        } else {
                            showAlert('Translation failed: ' + errorMessage, 'danger');
                        }
                    }
                })
                .catch(error => {
                    console.error('Translation error:', error);
                    console.log('Error details:', error.message, error.stack); // More detailed error logging
                    translateBtn.disabled = false;
                    translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Start Translation';

                    // Also reset the continue translation button
                    continueTranslationBtn.disabled = false;
                    continueTranslationBtn.innerHTML = '<i class="fas fa-check me-2"></i>Continue with Full Translation';

                    showAlert('Translation failed due to network error', 'danger');

            // Re-enable the remove file button after translation error
            removeFile.disabled = false;
            removeFile.style.opacity = '1';
            removeFile.style.cursor = 'pointer';
            
            // Note: File cleanup now only happens on new file upload
            // Removed cleanup here to preserve user files
            currentSessionId = null;
        });
    }

    // Function to generate and populate context prompt based on uploaded file
    function generateContextPrompt(forceRegenerate = false) {
        const fileContextTextarea = document.getElementById('fileContext');
        const regeneratePromptBtn = document.getElementById('regeneratePromptBtn');

        if (!fileContextTextarea) {
            console.warn('File context textarea not found');
            return;
        }

        // Don't overwrite existing content unless it's empty or forced
        if (!forceRegenerate && fileContextTextarea.value.trim() !== '') {
            console.log('File context already has content, skipping auto-generation');
            return;
        }

        // Get currently selected target languages for better prompt generation
        const selectedLanguages = getSelectedTargetLanguages();

        console.log('Generating context prompt for uploaded file...');

        // Show loading state on button if it exists
        if (regeneratePromptBtn) {
            regeneratePromptBtn.disabled = true;
            regeneratePromptBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Generating...';
        }

        fetch('/translator/api/generate-prompt', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                target_languages: selectedLanguages
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.prompt) {
                fileContextTextarea.value = data.prompt;
                console.log('Context prompt generated and populated');
                console.log('Debug info:', data.debug_info);
                console.log('Is EES report:', data.is_ees_report);

                // Show a subtle notification if it's an EES report
                if (data.is_ees_report) {
                    showAlert('EES report detected - specialized translation references loaded', 'info');
                } else if (forceRegenerate) {
                    showAlert('Context prompt generated successfully', 'success');
                }

                // Trigger any change handlers
                fileContextTextarea.dispatchEvent(new Event('input'));
            } else {
                console.warn('Failed to generate prompt:', data.error || 'Unknown error');
                if (forceRegenerate) {
                    showAlert('Failed to generate prompt: ' + (data.error || 'Unknown error'), 'warning');
                }
            }
        })
        .catch(error => {
            console.error('Error generating context prompt:', error);
            if (forceRegenerate) {
                showAlert('Error generating context prompt', 'danger');
            }
        })
        .finally(() => {
            // Reset button state
            if (regeneratePromptBtn) {
                regeneratePromptBtn.disabled = false;
                regeneratePromptBtn.innerHTML = '<i class="fas fa-magic me-1"></i>Generate Prompt';
            }
        });
    }
});
</script>
{% endblock %}