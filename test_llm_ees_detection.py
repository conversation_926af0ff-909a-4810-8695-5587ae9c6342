#!/usr/bin/env python3
"""
Test script to verify LLM-based EES detection logic in PromptGenerator
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# Add the src directory to the path so we can import the modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_llm_ees_detection():
    """Test LLM-based EES detection with various file types and content."""
    
    # Mock Flask app context for testing
    class MockApp:
        def __init__(self):
            self.logger = MockLogger()
            self.config = {
                "TRANSLATOR_LLM": None  # This will cause LLM detection to fail gracefully
            }
    
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
    
    # Mock current_app
    import src.backend.blueprints.translator_bot.prompt_generator as pg_module
    pg_module.current_app = MockApp()
    
    from src.backend.blueprints.translator_bot.prompt_generator import PromptGenerator
    
    # Initialize PromptGenerator
    generator = PromptGenerator()
    
    # Test cases - Note: EES reports are ONLY PowerPoint files (.pptx)
    test_cases = [
        {
            "name": "EES Report - PowerPoint with Strong Content",
            "filename": "Employee_Engagement_Survey_2024.pptx",
            "content": """Employee Engagement Survey 2024 - Electrolux Professional Group

            This annual employee engagement survey is designed to measure the engagement level of our workforce and identify areas for improvement. The survey focuses on high performance organizations and includes the HIPO index metrics.

            Key Areas Covered:
            1. How satisfied are you with your employment at the company in general?
            2. Based on my experience I would recommend the company as an employer to my friends and family
            3. I am proud working for my company and gladly tell people about it
            4. I believe strongly in and support the company's future direction
            5. How would you rate the engagement level of your direct colleagues?

            Leadership and Management:
            - Immediate manager effectiveness
            - Top management communication
            - Business unit collaboration
            - Customer focused initiatives
            - eNPS analysis and benchmarking""",
            "expected": True
        },
        {
            "name": "EES Report - PowerPoint with Filename Pattern",
            "filename": "ees_results_2024.pptx",
            "content": "Survey results and analysis for organizational development. Participation metrics: 85% response rate. Key engagement indices across strategic areas.",
            "expected": True
        },
        {
            "name": "Non-EES Document - Excel File (Cannot be EES)",
            "filename": "Employee_Engagement_Survey_2024.xlsx",
            "content": "Employee engagement survey data with all the right keywords but wrong file type",
            "expected": False
        },
        {
            "name": "Non-EES Document - Word Document",
            "filename": "installation_manual.docx",
            "content": "Manuel d'installation Essoreuse laveuse WUD718CV, WUD725CV, WUD730CV installation instructions and technical specifications for commercial laundry equipment",
            "expected": False
        },
        {
            "name": "Non-EES Document - PowerPoint but Wrong Content",
            "filename": "quarterly_financial_report.pptx",
            "content": "Q3 2024 Financial Results - Revenue, expenses, profit margins, and budget analysis for the quarter",
            "expected": False
        },
        {
            "name": "EES Report - PowerPoint with Survey Questions",
            "filename": "employee_feedback_survey.pptx",
            "content": """Employee Satisfaction Survey Results

            Participation Rate: 78%
            eNPS Score: +42

            Survey Questions Analysis:
            1. I am satisfied with my current role and responsibilities - 4.2/5
            2. My immediate manager provides clear direction and support - 3.8/5
            3. I feel valued and recognized for my contributions - 3.9/5
            4. The company culture promotes collaboration and teamwork - 4.1/5
            5. I would recommend this company as a great place to work - 4.0/5
            6. Leadership effectively communicates company vision and goals - 3.7/5
            7. I have opportunities for professional development and growth - 3.6/5

            Year-over-year trends show improvement in all areas.""",
            "expected": True
        }
    ]
    
    print("Testing LLM-based EES Detection Logic")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Filename: {test_case['filename']}")
        print(f"Content preview: {test_case['content'][:150]}...")
        
        # Create file analysis dict (simulating what _analyze_file would return)
        file_analysis = {
            'filename': test_case['filename'],
            'content_sample': test_case['content'],
            'file_type': 'pptx' if test_case['filename'].endswith('.pptx') else 'excel' if test_case['filename'].endswith('.xlsx') else 'word' if test_case['filename'].endswith('.docx') else 'pdf'
        }
        
        # Test detection
        is_ees = generator._is_ees_report(file_analysis)
        
        print(f"Expected: {test_case['expected']}")
        print(f"Actual: {is_ees}")
        print(f"Result: {'✓ PASS' if is_ees == test_case['expected'] else '✗ FAIL'}")
        
        # Test prompt generation for EES files
        if is_ees:
            prompt = generator._generate_ees_prompt(file_analysis, ['zh', 'es'])
            print(f"EES Prompt length: {len(prompt)} characters")
            print(f"Contains translation references: {'translation reference' in prompt.lower()}")

if __name__ == "__main__":
    test_llm_ees_detection()
