import json
import os
import re
from typing import Dict, List, Optional
from flask import current_app
import pandas as pd
from docx import Document
from pptx import Presentation
from PyPDF2 import PdfReader


class PromptGenerator:
    """
    Generates contextual prompts for file translation based on file content analysis.
    Includes special handling for EES (Employee Engagement Survey) reports.
    """

    def __init__(self):
        self.config_path = os.path.join(os.path.dirname(__file__), 'config', 'ees_report_config.json')
        self.ees_translations_dir = os.path.join(os.path.dirname(__file__), 'ees')
        self.config = self._load_config()

        # Language mapping for EES translations
        self.language_mapping = {
            'zh': 'Chinese', 
            'en': 'English', 
            'fr': 'French', 
            'de': 'German',
            'it': 'Italian', 
            'ja': 'Japanese', 
            'es': 'Spanish', 
            'sv': 'Swedish',
            'th': 'Thai', 
            'tu': 'Turkish'
        }

    def _load_config(self) -> Dict:
        """Load the EES report configuration."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            current_app.logger.error(f"Failed to load EES config: {e}")
            return {}

    def _load_ees_translations(self, target_language: str) -> Optional[Dict]:
        """Load EES translation references for the target language."""
        try:
            lang_key = target_language.lower()
            if lang_key not in self.language_mapping:
                return None

            filename = f"translations_{self.language_mapping[lang_key]}.json"
            filepath = os.path.join(self.ees_translations_dir, filename)

            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            current_app.logger.error(f"Failed to load EES translations for {target_language}: {e}")
            return None

    def generate_prompt(self, file_path: str, target_languages: List[str] = None,
                       original_filename: str = None) -> str:
        """Generate a contextual prompt based on file analysis."""
        try:
            file_analysis = self._analyze_file(file_path, original_filename)
            is_ees_report = self._is_ees_report(file_analysis)
            if is_ees_report:
                # Always use EES-specific prompt and pass target_languages
                return self._generate_ees_prompt(file_analysis, target_languages)
            else:
                return self._generate_content_specific_prompt(file_analysis, target_languages)

        except Exception as e:
            current_app.logger.error(f"Error generating prompt: {e}")
            return "Please translate this document accurately while maintaining the original meaning and context."

    def _analyze_file(self, file_path: str, original_filename: str = None) -> Dict:
        """Analyze file content to extract key information."""
        analysis = {
            'file_type': self._get_file_type(file_path),
            'filename': original_filename or os.path.basename(file_path),
            'content_sample': '',
            'columns': []
        }

        try:
            # Dispatch to appropriate analyzer
            analyzers = {
                'excel': self._analyze_excel,
                'word': self._analyze_word,
                'powerpoint': self._analyze_powerpoint,
                'pdf': self._analyze_pdf
            }

            analyzer = analyzers.get(analysis['file_type'])
            if analyzer:
                analysis.update(analyzer(file_path))

        except Exception as e:
            current_app.logger.error(f"Error analyzing file {file_path}: {e}")

        return analysis
    

    def _get_file_type(self, file_path: str) -> str:
        """Determine file type from extension."""
        ext = os.path.splitext(file_path)[1].lower()
        type_mapping = {
            '.xlsx': 'excel', '.xls': 'excel',
            '.docx': 'word', '.doc': 'word',
            '.pptx': 'powerpoint', '.ppt': 'powerpoint',
            '.pdf': 'pdf'
        }
        return type_mapping.get(ext, 'unknown')
    

    def _analyze_excel(self, file_path: str) -> Dict:
        """Analyze Excel file content."""
        try:
            df = pd.read_excel(file_path, nrows=50)
            columns = df.columns.tolist()

            sample_data = []
            for col in columns:
                sample_values = df[col].dropna().astype(str).head(50).tolist()
                sample_data.extend(sample_values)

            content_sample = ' '.join(sample_data)[:2000]
            full_content = ' '.join(columns) + ' ' + content_sample

            return {
                'content_sample': content_sample,
                'columns': columns,
                'row_count': len(df),
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing Excel file: {e}")
            return {'content_sample': '', 'columns': [], 'row_count': 0}
        

    def _analyze_word(self, file_path: str) -> Dict:
        """Analyze Word document content."""
        try:
            doc = Document(file_path)
            paragraphs = [p.text for p in doc.paragraphs if p.text.strip()]
            content_sample = ' '.join(paragraphs[:20])[:2000]

            # Extract headings and table content
            headings = [p.text.strip() for p in doc.paragraphs
                       if p.style.name.startswith('Heading') and p.text.strip()]

            table_content = []
            for table in doc.tables[:3]:
                for row in table.rows[:5]:
                    row_text = [cell.text.strip() for cell in row.cells]
                    table_content.extend(row_text)

            full_content = content_sample + ' ' + ' '.join(table_content) + ' ' + ' '.join(headings)

            return {
                'content_sample': full_content[:2000],
                'paragraph_count': len(paragraphs),
                'heading_count': len(headings),
                'table_count': len(doc.tables),
                'has_tables': len(doc.tables) > 0,
                'has_headings': len(headings) > 0
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing Word document: {e}")
            return {'content_sample': '', 'paragraph_count': 0}
        

    def _analyze_powerpoint(self, file_path: str) -> Dict:
        """Analyze PowerPoint presentation content."""
        try:
            prs = Presentation(file_path)
            text_content = []

            for slide_num, slide in enumerate(prs.slides):
                if slide_num >= 10:  # Limit to first 10 slides
                    break
                try:
                    for shape in slide.shapes:
                        if hasattr(shape, "text") and shape.text.strip():
                            text_content.append(shape.text.strip())
                except Exception:
                    continue

            content_sample = ' '.join(text_content)[:2000]

            return {
                'content_sample': content_sample,
                'slide_count': len(prs.slides)
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing PowerPoint: {e}")
            return {'content_sample': '', 'slide_count': 0}
        

    def _analyze_pdf(self, file_path: str) -> Dict:
        """Analyze PDF content."""
        try:
            reader = PdfReader(file_path)
            text_content = []

            for page_num in range(min(3, len(reader.pages))):
                page = reader.pages[page_num]
                text_content.append(page.extract_text())

            content_sample = ' '.join(text_content)[:2000]

            return {
                'content_sample': content_sample,
                'page_count': len(reader.pages)    
            }
        except Exception as e:
            current_app.logger.error(f"Error analyzing PDF: {e}")
            return {'content_sample': '', 'page_count': 0}


    def _is_ees_report(self, file_analysis: Dict) -> bool:
        """Determine if the file is an EES (Employee Engagement Survey) report using LLM analysis."""
        if not self.config.get('ees_report_detection'):
            return False

        # Try LLM-based detection first
        try:
            llm_result = self._llm_based_ees_detection(file_analysis)
            if llm_result is not None:
                current_app.logger.info(f"LLM-based EES detection result: {llm_result}")
                return llm_result
        except Exception as e:
            current_app.logger.warning(f"LLM-based EES detection failed, falling back to rule-based: {e}")

        # Fallback to rule-based detection
        return self._rule_based_ees_detection(file_analysis)

    def _llm_based_ees_detection(self, file_analysis: Dict) -> bool:
        """Use LLM to determine if the file is an EES report."""
        try:
            translator_llm = current_app.config["TRANSLATOR_LLM"]
            if not translator_llm:
                return None

            filename = file_analysis.get('filename', '')
            content_sample = file_analysis.get('content_sample', '')
            file_type = file_analysis.get('file_type', '')
            columns = file_analysis.get('columns', [])

            # Prepare analysis content for LLM
            analysis_content = f"""
Filename: {filename}
File Type: {file_type}
Content Sample: {content_sample[:1500]}
"""
            if columns:
                analysis_content += f"\nColumn Names: {', '.join(columns[:20])}"

            system_prompt = """You are an expert document classifier specializing in Employee Engagement Survey (EES) reports.

An EES report typically contains:
- Employee satisfaction surveys or questionnaires
- Engagement metrics, scores, or ratings
- Workplace culture assessments
- Leadership effectiveness evaluations
- Employee feedback or survey results
- Organizational development content
- Terms like "engagement", "satisfaction", "survey", "employee feedback"
- Company-specific engagement frameworks (like HIPO index, engagement plus)
- References to managers, leadership, business units in survey context

Analyze the provided document information and determine if this is an Employee Engagement Survey report or related employee feedback document.

Respond with only "YES" if this is an EES report, or "NO" if it is not. Be confident in your assessment."""

            user_prompt = f"""Please analyze this document and determine if it's an Employee Engagement Survey (EES) report:

{analysis_content}

Is this an Employee Engagement Survey report? Answer YES or NO only."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # Get the deployment name from config
            from config.config import TranslatorBotConfig
            config = TranslatorBotConfig()

            response = translator_llm.chat.completions.create(
                model=config.translator_llm__deployment,
                messages=messages,
                temperature=0.1,
                top_p=0.9,
                max_tokens=10
            )

            result = response.choices[0].message.content.strip().upper()

            # Log the LLM decision for debugging
            current_app.logger.info(f"LLM EES Detection:")
            current_app.logger.info(f"  - Filename: {filename}")
            current_app.logger.info(f"  - Content length: {len(content_sample)}")
            current_app.logger.info(f"  - LLM Response: {result}")

            if result == "YES":
                return True
            elif result == "NO":
                return False
            else:
                current_app.logger.warning(f"Unexpected LLM response for EES detection: {result}")
                return None

        except Exception as e:
            current_app.logger.error(f"Error in LLM-based EES detection: {e}")
            return None

    def _rule_based_ees_detection(self, file_analysis: Dict) -> bool:
        """Fallback rule-based EES detection method."""
        detection_config = self.config['ees_report_detection']
        strong_indicators = detection_config.get('strong_indicators', [])
        weak_indicators = detection_config.get('weak_indicators', [])
        file_patterns = detection_config.get('file_patterns', [])

        filename = file_analysis.get('filename', '').lower()
        content = file_analysis.get('content_sample', '').lower()

        strong_score = weak_score = 0

        # Check filename patterns from config
        for pattern in file_patterns:
            # Convert glob pattern to regex
            regex_pattern = pattern.replace('*', '.*').lower()
            if re.search(regex_pattern, filename):
                strong_score += 2
                break  # Only count once for filename match

        # Fallback patterns if no config patterns match
        if strong_score == 0:
            ees_patterns = ['.*ees.*', '.*employee.*engagement.*survey.*']
            if any(re.search(pattern, filename) for pattern in ees_patterns):
                strong_score += 2

        # Check content indicators
        strong_score += sum(2 for indicator in strong_indicators if indicator.lower() in content)
        weak_score += sum(1 for indicator in weak_indicators if indicator.lower() in content)

        total_score = strong_score + weak_score
        has_content = len(content.strip()) > 50

        # More lenient detection logic - if we have any strong indicators, it's likely an EES report
        is_ees = False
        if strong_score >= 2:
            is_ees = True
        elif has_content and total_score >= 3:  # Lowered threshold
            is_ees = True
        else:
            is_ees = False

        # Log detection details for debugging
        current_app.logger.info(f"Rule-based EES Detection Analysis:")
        current_app.logger.info(f"  - Filename: {filename}")
        current_app.logger.info(f"  - Content length: {len(content)}")
        current_app.logger.info(f"  - Strong score: {strong_score}")
        current_app.logger.info(f"  - Weak score: {weak_score}")
        current_app.logger.info(f"  - Total score: {total_score}")
        current_app.logger.info(f"  - Has content: {has_content}")
        current_app.logger.info(f"  - Is EES: {is_ees}")

        return is_ees
    

    def _generate_content_specific_prompt(self, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """Generate content-aware prompts based on file analysis."""

        try:
            # Try LLM-based prompt generation
            return self._generate_llm_based_prompt(file_analysis, target_languages)
        except Exception as e:
            current_app.logger.error(f"LLM prompt generation failed: {e}")
            return self._use_fallback_prompt(file_analysis, target_languages)


    def _generate_ees_content(self, target_languages: List[str]) -> str:
        """Generate EES-specific content including translation references."""
        ees_content_parts = []

        for language in target_languages:
            translations = self._load_ees_translations(language)
            if translations:
                display_name = self._get_language_display_name(language)
                ees_content_parts.append(f"\nFor {display_name} translation:")
                ees_content_parts.append("Use these as a translation reference:")

                for english_text, translated_text in translations.items():
                    ees_content_parts.append(f"'{english_text}' : '{translated_text}'")

        return '\n'.join(ees_content_parts)
    

    def _get_language_display_name(self, language_code: str) -> str:
        """Convert language code to display name."""
        # If it's already a full name, return it capitalized
        if language_code.lower() in ['chinese', 'english', 'french', 'german', 'italian',
                                   'japanese', 'spanish', 'swedish', 'thai', 'turkish']:
            return language_code.title()

        # Convert code to name using the mapping
        return self.language_mapping.get(language_code.lower(), language_code.title())
    

    def _generate_llm_based_prompt(self, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """Generate intelligent translation prompts using LLM."""
        translator_llm = current_app.config["TRANSLATOR_LLM"]
        analysis_summary = self._prepare_document_analysis_summary(file_analysis, target_languages)

        system_prompt = """You are an expert translation consultant. Create a concise translation prompt that:
1. Identifies the document type and purpose
2. Provides specific guidance for content and structure
3. Considers target language(s) and cultural adaptation
4. Gives instructions for maintaining document integrity

Keep it focused, professional, and under 200 words. Return only the translation prompt."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": analysis_summary}
        ]

        # Get the deployment name from config
        from config.config import TranslatorBotConfig
        config = TranslatorBotConfig()

        response = translator_llm.chat.completions.create(
            model=config.translator_llm__deployment,
            messages=messages,
            temperature=0.1,
            top_p=0.9
        )

        return response.choices[0].message.content.strip()
    

    def _prepare_document_analysis_summary(self, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """Prepare a structured summary of document analysis for the LLM."""
        summary_parts = [
            f"DOCUMENT ANALYSIS:",
            f"- File type: {file_analysis.get('file_type', 'unknown')}",
            f"- Filename: {file_analysis.get('filename', 'unknown')}",
        ]

        # Document structure
        if file_analysis.get('paragraph_count'):
            summary_parts.append(f"- Paragraphs: {file_analysis['paragraph_count']}")
        if file_analysis.get('heading_count'):
            summary_parts.append(f"- Headings: {file_analysis['heading_count']}")
        if file_analysis.get('table_count'):
            summary_parts.append(f"- Tables: {file_analysis['table_count']}")


        # Content preview
        content_sample = file_analysis.get('content_sample', '')
        if content_sample:
            preview = content_sample[:300] + ("..." if len(content_sample) > 300 else "")
            summary_parts.extend(["\nCONTENT PREVIEW:", preview])

        # Target languages
        if target_languages:
            lang_names = [self._get_language_display_name(lang) for lang in target_languages]
            summary_parts.append(f"\nTARGET LANGUAGES: {', '.join(lang_names)}")

        return '\n'.join(summary_parts)


    def _use_fallback_prompt(self, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """Return a generic translation prompt when LLM generation fails."""
        return (
            "Please translate this document accurately, maintaining the original meaning, context, and structure. "
            "Ensure terminology consistency and adapt the language appropriately for the target audience."
        )
    

    def _generate_ees_prompt(self, file_analysis: Dict, target_languages: List[str] = None) -> str:
        """Return a standard prompt for EES reports, optionally with translation references."""
        prompt = (
            "This is an Employee Engagement Survey (EES) report. "
            "Please translate all survey questions, instructions, and results accurately, maintaining the original meaning and context. "
            "Preserve the structure of tables and sections, and ensure terminology consistency throughout the document. "
            "Adapt the language appropriately for the target audience."
        )

        if target_languages:
            ees_content_parts = []
            for language in target_languages:
                translations = self._load_ees_translations(language)
                if translations:
                    display_name = self._get_language_display_name(language)
                    ees_content_parts.append(f"\nFor {display_name} translation use these as a translation reference:")
                    for english_text, translated_text in translations.items():
                        ees_content_parts.append(f"'{english_text}' : '{translated_text}'")
            if ees_content_parts:
                prompt += "\n\n" + '\n'.join(ees_content_parts)
        return prompt
