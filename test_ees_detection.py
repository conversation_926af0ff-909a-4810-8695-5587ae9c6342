#!/usr/bin/env python3
"""
Test script to verify EES detection logic in PromptGenerator
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# Add the src directory to the path so we can import the modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from backend.blueprints.translator_bot.prompt_generator import PromptGenerator

def create_test_file(filename: str, content: str) -> str:
    """Create a temporary test file with the given content."""
    temp_dir = tempfile.mkdtemp()
    file_path = os.path.join(temp_dir, filename)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return file_path

def test_ees_detection():
    """Test EES detection with various file types and content."""
    
    # Mock Flask app context for testing
    class MockApp:
        def __init__(self):
            self.logger = MockLogger()
            self.config = {"TRANSLATOR_LLM": None}
    
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
    
    # Mock current_app
    import src.backend.blueprints.translator_bot.prompt_generator as pg_module
    pg_module.current_app = MockApp()
    
    # Initialize PromptGenerator
    generator = PromptGenerator()
    
    # Test cases
    test_cases = [
        {
            "name": "EES Report - Filename Match",
            "filename": "Employee_Engagement_Survey_2024.xlsx",
            "content": "How satisfied are you with your employment? Leadership effectiveness survey results",
            "expected": True
        },
        {
            "name": "EES Report - Content Match",
            "filename": "survey_results.xlsx", 
            "content": "Employee engagement survey results for Electrolux Professional Group. High performance organizations analysis with HIPO index metrics.",
            "expected": True
        },
        {
            "name": "EES Report - Weak Indicators",
            "filename": "feedback_report.xlsx",
            "content": "Electrolux Professional immediate manager feedback. Top management leadership survey results engagement level customer focused business unit analysis.",
            "expected": True
        },
        {
            "name": "Non-EES Document",
            "filename": "installation_manual.docx",
            "content": "Manuel d'installation Essoreuse laveuse WUD718CV, WUD725CV, WUD730CV installation instructions and technical specifications",
            "expected": False
        },
        {
            "name": "EES Filename Pattern",
            "filename": "ees_report_2024.pdf",
            "content": "Annual report with various metrics and analysis",
            "expected": True
        }
    ]
    
    print("Testing EES Detection Logic")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Filename: {test_case['filename']}")
        print(f"Content: {test_case['content'][:100]}...")
        
        # Create file analysis dict (simulating what _analyze_file would return)
        file_analysis = {
            'filename': test_case['filename'],
            'content_sample': test_case['content'],
            'file_type': 'excel' if test_case['filename'].endswith('.xlsx') else 'word' if test_case['filename'].endswith('.docx') else 'pdf'
        }
        
        # Test detection
        is_ees = generator._is_ees_report(file_analysis)
        
        print(f"Expected: {test_case['expected']}")
        print(f"Actual: {is_ees}")
        print(f"Result: {'✓ PASS' if is_ees == test_case['expected'] else '✗ FAIL'}")
        
        if is_ees == test_case['expected']:
            # Test prompt generation
            if is_ees:
                prompt = generator._generate_ees_prompt(file_analysis, ['zh', 'es'])
                print(f"EES Prompt generated: {len(prompt)} characters")
                print(f"Contains translation references: {'translation reference' in prompt.lower()}")
            else:
                prompt = generator._use_fallback_prompt(file_analysis, ['zh', 'es'])
                print(f"Fallback prompt generated: {len(prompt)} characters")

if __name__ == "__main__":
    test_ees_detection()
