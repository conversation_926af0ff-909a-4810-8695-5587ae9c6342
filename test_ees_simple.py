#!/usr/bin/env python3
"""
Simple test to verify EES detection logic
"""

import sys
import os
import json

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ees_detection_simple():
    """Test EES detection with sample content."""
    
    # Mock Flask app context
    class MockApp:
        def __init__(self):
            self.logger = MockLogger()
            self.config = {"TRANSLATOR_LLM": None}
    
    class MockLogger:
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
    
    # Mock current_app
    import src.backend.blueprints.translator_bot.prompt_generator as pg_module
    pg_module.current_app = MockApp()
    
    from src.backend.blueprints.translator_bot.prompt_generator import PromptGenerator
    
    # Initialize PromptGenerator
    generator = PromptGenerator()
    
    # Read the EES sample content
    with open('test_ees_sample.txt', 'r', encoding='utf-8') as f:
        ees_content = f.read()
    
    # Test cases
    test_cases = [
        {
            "name": "EES Report - Strong Content Match",
            "filename": "Employee_Engagement_Survey_2024.xlsx",
            "content": ees_content,
            "expected": True
        },
        {
            "name": "EES Report - Filename Only",
            "filename": "ees_results.xlsx",
            "content": "Some general content without specific EES indicators",
            "expected": True
        },
        {
            "name": "Non-EES Document",
            "filename": "installation_manual.docx",
            "content": "Manuel d'installation Essoreuse laveuse WUD718CV installation instructions",
            "expected": False
        }
    ]
    
    print("Testing EES Detection Logic")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Filename: {test_case['filename']}")
        print(f"Content preview: {test_case['content'][:100]}...")
        
        # Create file analysis dict
        file_analysis = {
            'filename': test_case['filename'],
            'content_sample': test_case['content'],
            'file_type': 'excel' if test_case['filename'].endswith('.xlsx') else 'word'
        }
        
        # Test detection
        is_ees = generator._is_ees_report(file_analysis)
        
        print(f"Expected: {test_case['expected']}")
        print(f"Actual: {is_ees}")
        print(f"Result: {'✓ PASS' if is_ees == test_case['expected'] else '✗ FAIL'}")
        
        # Test prompt generation for EES files
        if is_ees:
            prompt = generator._generate_ees_prompt(file_analysis, ['zh', 'es'])
            print(f"EES Prompt length: {len(prompt)} characters")
            print(f"Contains Chinese references: {'Chinese' in prompt}")
            print(f"Contains Spanish references: {'Spanish' in prompt}")

if __name__ == "__main__":
    test_ees_detection_simple()
